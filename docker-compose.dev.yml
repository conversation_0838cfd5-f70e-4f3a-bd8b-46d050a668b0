version: '3.8'

services:
  # CVmatic Web Application - Development Mode
  cvmatic-web-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - cvmatic-network
    restart: unless-stopped
    command: npm run dev

  # Redis for development
  redis-dev:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - cvmatic-network
    restart: unless-stopped

networks:
  cvmatic-network:
    driver: bridge
