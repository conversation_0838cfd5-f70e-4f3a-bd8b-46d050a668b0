import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable standalone output for Docker
  output: 'standalone',

  // Disable ESLint during build for now
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Configure image optimization
  images: {
    unoptimized: true,
  },

  // External packages for server components
  serverExternalPackages: ['sharp'],

  // Configure file upload limits
  serverRuntimeConfig: {
    maxFileSize: '4mb',
  },

  // Public runtime config
  publicRuntimeConfig: {
    maxFileSize: '4mb',
  },
};

export default nextConfig;
