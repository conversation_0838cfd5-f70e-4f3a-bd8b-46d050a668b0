import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable standalone output for Docker
  output: 'standalone',

  // Configure image optimization
  images: {
    unoptimized: true,
  },

  // Enable experimental features
  experimental: {
    // Enable server components
    serverComponentsExternalPackages: [],
  },

  // Configure file upload limits
  serverRuntimeConfig: {
    maxFileSize: '4mb',
  },

  // Public runtime config
  publicRuntimeConfig: {
    maxFileSize: '4mb',
  },
};

export default nextConfig;
