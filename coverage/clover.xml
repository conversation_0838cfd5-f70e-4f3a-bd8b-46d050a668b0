<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1751717496465" clover="3.2.0">
  <project timestamp="1751717496465" name="All files">
    <metrics statements="391" coveredstatements="167" conditionals="271" coveredconditionals="90" methods="121" coveredmethods="38" elements="783" coveredelements="295" complexity="0" loc="391" ncloc="391" packages="7" files="20" classes="20"/>
    <package name="app">
      <metrics statements="14" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="page.tsx" path="/Users/<USER>/Desktop/cvmatic/src/app/page.tsx">
        <metrics statements="14" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.dashboard">
      <metrics statements="26" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="page.tsx" path="/Users/<USER>/Desktop/cvmatic/src/app/dashboard/page.tsx">
        <metrics statements="26" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.upload">
      <metrics statements="26" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="page.tsx" path="/Users/<USER>/Desktop/cvmatic/src/app/upload/page.tsx">
        <metrics statements="26" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.layout">
      <metrics statements="72" coveredstatements="0" conditionals="88" coveredconditionals="0" methods="17" coveredmethods="0"/>
      <file name="Footer.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/layout/Footer.tsx">
        <metrics statements="12" coveredstatements="0" conditionals="49" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
      </file>
      <file name="Header.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/layout/Header.tsx">
        <metrics statements="18" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
      </file>
      <file name="Layout.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/layout/Layout.tsx">
        <metrics statements="39" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/cvmatic/src/components/layout/index.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.ui">
      <metrics statements="72" coveredstatements="24" conditionals="12" coveredconditionals="2" methods="40" coveredmethods="5"/>
      <file name="alert.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/ui/alert.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="66" count="3" type="stmt"/>
      </file>
      <file name="badge.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/ui/badge.tsx">
        <metrics statements="7" coveredstatements="5" conditionals="3" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
      </file>
      <file name="button.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/ui/button.tsx">
        <metrics statements="7" coveredstatements="7" conditionals="3" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="48" count="28" type="cond" truecount="1" falsecount="1"/>
        <line num="59" count="28" type="stmt"/>
      </file>
      <file name="card.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/ui/card.tsx">
        <metrics statements="9" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="7" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="85" count="28" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="20" type="stmt"/>
      </file>
      <file name="dialog.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/ui/dialog.tsx">
        <metrics statements="14" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
      </file>
      <file name="dropdown-menu.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/ui/dropdown-menu.tsx">
        <metrics statements="19" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
      </file>
      <file name="input.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/ui/input.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
      </file>
      <file name="label.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/ui/label.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
      </file>
      <file name="progress.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/ui/progress.tsx">
        <metrics statements="4" coveredstatements="3" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.upload">
      <metrics statements="77" coveredstatements="55" conditionals="73" coveredconditionals="42" methods="31" coveredmethods="20"/>
      <file name="FileUpload.tsx" path="/Users/<USER>/Desktop/cvmatic/src/components/upload/FileUpload.tsx">
        <metrics statements="76" coveredstatements="55" conditionals="73" coveredconditionals="42" methods="31" coveredmethods="20"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="38" count="11" type="stmt"/>
        <line num="44" count="20" type="stmt"/>
        <line num="45" count="20" type="stmt"/>
        <line num="46" count="20" type="stmt"/>
        <line num="48" count="20" type="stmt"/>
        <line num="51" count="8" type="cond" truecount="1" falsecount="1"/>
        <line num="54" count="20" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="20" type="stmt"/>
        <line num="83" count="8" type="stmt"/>
        <line num="86" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="92" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="102" count="7" type="stmt"/>
        <line num="105" count="7" type="stmt"/>
        <line num="111" count="7" type="stmt"/>
        <line num="113" count="7" type="stmt"/>
        <line num="115" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="127" count="1" type="stmt"/>
        <line num="131" count="6" type="stmt"/>
        <line num="134" count="6" type="stmt"/>
        <line num="135" count="6" type="stmt"/>
        <line num="136" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="148" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="149" count="6" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="6" type="stmt"/>
        <line num="155" count="6" type="stmt"/>
        <line num="156" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="20" type="stmt"/>
        <line num="177" count="8" type="stmt"/>
        <line num="180" count="20" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="20" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="199" count="20" type="stmt"/>
        <line num="200" count="8" type="cond" truecount="1" falsecount="1"/>
        <line num="201" count="8" type="stmt"/>
        <line num="202" count="8" type="stmt"/>
        <line num="203" count="8" type="stmt"/>
        <line num="204" count="8" type="stmt"/>
        <line num="266" count="8" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/cvmatic/src/components/upload/index.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
      </file>
    </package>
    <package name="lib">
      <metrics statements="104" coveredstatements="88" conditionals="69" coveredconditionals="46" methods="14" coveredmethods="13"/>
      <file name="security.ts" path="/Users/<USER>/Desktop/cvmatic/src/lib/security.ts">
        <metrics statements="100" coveredstatements="84" conditionals="69" coveredconditionals="46" methods="13" coveredmethods="12"/>
        <line num="6" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="54" count="6" type="stmt"/>
        <line num="55" count="8" type="stmt"/>
        <line num="58" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="59" count="2" type="stmt"/>
        <line num="66" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="67" count="1" type="stmt"/>
        <line num="74" count="5" type="stmt"/>
        <line num="75" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="76" count="1" type="stmt"/>
        <line num="82" count="4" type="stmt"/>
        <line num="88" count="3" type="stmt"/>
        <line num="89" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="90" count="2" type="stmt"/>
        <line num="96" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="97" count="1" type="stmt"/>
        <line num="103" count="2" type="stmt"/>
        <line num="109" count="6" type="stmt"/>
        <line num="111" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="112" count="2" type="stmt"/>
        <line num="119" count="6" type="cond" truecount="5" falsecount="0"/>
        <line num="120" count="1" type="stmt"/>
        <line num="127" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="128" count="1" type="stmt"/>
        <line num="135" count="4" type="stmt"/>
        <line num="136" count="4" type="stmt"/>
        <line num="137" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="138" count="1" type="stmt"/>
        <line num="145" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="146" count="1" type="stmt"/>
        <line num="152" count="2" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="176" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="183" count="0" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="200" count="2" type="stmt"/>
        <line num="201" count="2" type="stmt"/>
        <line num="202" count="2" type="stmt"/>
        <line num="205" count="2" type="stmt"/>
        <line num="213" count="2" type="stmt"/>
        <line num="214" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="215" count="1" type="stmt"/>
        <line num="219" count="2" type="stmt"/>
        <line num="220" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="221" count="1" type="stmt"/>
        <line num="225" count="2" type="stmt"/>
        <line num="226" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="227" count="1" type="stmt"/>
        <line num="231" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="236" count="2" type="stmt"/>
        <line num="247" count="5" type="stmt"/>
        <line num="249" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="252" count="5" type="stmt"/>
        <line num="259" count="5" type="cond" truecount="3" falsecount="1"/>
        <line num="260" count="0" type="stmt"/>
        <line num="264" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="271" count="4" type="stmt"/>
        <line num="277" count="5" type="stmt"/>
        <line num="278" count="5" type="stmt"/>
        <line num="283" count="5" type="stmt"/>
        <line num="284" count="5" type="stmt"/>
        <line num="288" count="7" type="stmt"/>
        <line num="289" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="292" count="12" type="stmt"/>
        <line num="295" count="7" type="stmt"/>
        <line num="297" count="7" type="stmt"/>
        <line num="301" count="12" type="stmt"/>
        <line num="302" count="12" type="cond" truecount="2" falsecount="0"/>
        <line num="303" count="12" type="stmt"/>
        <line num="304" count="12" type="stmt"/>
        <line num="308" count="2" type="stmt"/>
        <line num="309" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="310" count="2" type="stmt"/>
        <line num="311" count="2" type="stmt"/>
        <line num="315" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="316" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
      </file>
      <file name="utils.ts" path="/Users/<USER>/Desktop/cvmatic/src/lib/utils.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="82" type="stmt"/>
        <line num="5" count="82" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
