{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cvmatic/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cvmatic/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cvmatic/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cvmatic/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cvmatic/src/lib/security.ts"], "sourcesContent": ["/**\n * Security utilities for file validation and protection\n */\n\n// File type validation\nexport const ALLOWED_FILE_TYPES = {\n  'application/pdf': ['.pdf'],\n  'application/msword': ['.doc'],\n  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']\n} as const;\n\nexport const ALLOWED_EXTENSIONS = ['.pdf', '.doc', '.docx'] as const;\n\n// File size limits\nexport const MAX_FILE_SIZE = 4 * 1024 * 1024; // 4MB in bytes\nexport const MIN_FILE_SIZE = 1024; // 1KB minimum\n\n// Security patterns to detect malicious content\nconst MALICIOUS_PATTERNS = [\n  // Script injection patterns\n  /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n  /javascript:/gi,\n  /vbscript:/gi,\n  /onload\\s*=/gi,\n  /onerror\\s*=/gi,\n\n  // File path traversal\n  /\\.\\.\\//g,\n  /\\.\\.\\\\\\\\/g,\n\n  // Executable extensions in filename\n  /\\.(exe|bat|cmd|com|pif|scr|vbs|js|jar|app|deb|pkg|dmg)$/i,\n\n  // Suspicious file headers (magic numbers)\n  /^MZ/, // PE executable\n  /^PK/, // ZIP archive (could contain malicious files)\n];\n\nexport interface FileValidationResult {\n  isValid: boolean;\n  errors: string[];\n  warnings: string[];\n  fileInfo: {\n    name: string;\n    size: number;\n    type: string;\n    extension: string;\n  };\n}\n\n/**\n * Validates file type based on MIME type and extension\n */\nexport function validateFileType(file: File): { isValid: boolean; error?: string } {\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n\n  // Check if extension is allowed\n  if (!ALLOWED_EXTENSIONS.includes(fileExtension as any)) {\n    return {\n      isValid: false,\n      error: `File type not allowed. Supported formats: ${ALLOWED_EXTENSIONS.join(', ')}`\n    };\n  }\n\n  // Check MIME type\n  if (!Object.keys(ALLOWED_FILE_TYPES).includes(file.type)) {\n    return {\n      isValid: false,\n      error: `Invalid MIME type. Expected: ${Object.keys(ALLOWED_FILE_TYPES).join(', ')}`\n    };\n  }\n\n  // Cross-check MIME type with extension\n  const allowedExtensions = ALLOWED_FILE_TYPES[file.type as keyof typeof ALLOWED_FILE_TYPES];\n  if (!allowedExtensions.includes(fileExtension as any)) {\n    return {\n      isValid: false,\n      error: 'File extension does not match MIME type'\n    };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Validates file size\n */\nexport function validateFileSize(file: File): { isValid: boolean; error?: string } {\n  if (file.size > MAX_FILE_SIZE) {\n    return {\n      isValid: false,\n      error: `File size exceeds maximum limit of ${Math.round(MAX_FILE_SIZE / (1024 * 1024))}MB`\n    };\n  }\n\n  if (file.size < MIN_FILE_SIZE) {\n    return {\n      isValid: false,\n      error: `File size is too small. Minimum size is ${MIN_FILE_SIZE} bytes`\n    };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Validates filename for security issues\n */\nexport function validateFileName(fileName: string): { isValid: boolean; error?: string } {\n  // Check for null bytes\n  if (fileName.includes('\\0')) {\n    return {\n      isValid: false,\n      error: 'Filename contains null bytes'\n    };\n  }\n\n  // Check for path traversal\n  if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\\\')) {\n    return {\n      isValid: false,\n      error: 'Filename contains invalid characters'\n    };\n  }\n\n  // Check for control characters\n  if (/[\\x00-\\x1f\\x7f-\\x9f]/.test(fileName)) {\n    return {\n      isValid: false,\n      error: 'Filename contains control characters'\n    };\n  }\n\n  // Check for reserved names (Windows)\n  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];\n  const nameWithoutExt = fileName.split('.')[0].toUpperCase();\n  if (reservedNames.includes(nameWithoutExt)) {\n    return {\n      isValid: false,\n      error: 'Filename uses reserved system name'\n    };\n  }\n\n  // Check length\n  if (fileName.length > 255) {\n    return {\n      isValid: false,\n      error: 'Filename is too long'\n    };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Scans file content for malicious patterns\n */\nexport async function scanFileContent(file: File): Promise<{ isValid: boolean; warnings: string[] }> {\n  const warnings: string[] = [];\n\n  try {\n    // Read first 1KB of file for header analysis\n    const headerBuffer = await file.slice(0, 1024).arrayBuffer();\n    const headerText = new TextDecoder('utf-8', { fatal: false }).decode(headerBuffer);\n\n    // Check for malicious patterns\n    for (const pattern of MALICIOUS_PATTERNS) {\n      if (pattern.test(headerText)) {\n        warnings.push('Potentially suspicious content detected in file header');\n        break;\n      }\n    }\n\n    // Check for embedded files (ZIP signature in PDF)\n    if (file.type === 'application/pdf' && headerText.includes('PK')) {\n      warnings.push('PDF contains embedded archive - please verify content');\n    }\n\n    // Check for macros in Office documents\n    if (file.name.toLowerCase().endsWith('.docx') || file.name.toLowerCase().endsWith('.doc')) {\n      // This is a simplified check - in production, you'd want more sophisticated macro detection\n      if (headerText.includes('macro') || headerText.includes('VBA')) {\n        warnings.push('Document may contain macros - please verify safety');\n      }\n    }\n\n  } catch (error) {\n    warnings.push('Unable to scan file content completely');\n  }\n\n  return {\n    isValid: warnings.length === 0,\n    warnings\n  };\n}\n\n/**\n * Comprehensive file validation\n */\nexport async function validateFile(file: File): Promise<FileValidationResult> {\n  const errors: string[] = [];\n  const warnings: string[] = [];\n\n  // Basic file info\n  const fileInfo = {\n    name: file.name,\n    size: file.size,\n    type: file.type,\n    extension: '.' + file.name.split('.').pop()?.toLowerCase() || ''\n  };\n\n  // Validate filename\n  const fileNameValidation = validateFileName(file.name);\n  if (!fileNameValidation.isValid) {\n    errors.push(fileNameValidation.error!);\n  }\n\n  // Validate file type\n  const fileTypeValidation = validateFileType(file);\n  if (!fileTypeValidation.isValid) {\n    errors.push(fileTypeValidation.error!);\n  }\n\n  // Validate file size\n  const fileSizeValidation = validateFileSize(file);\n  if (!fileSizeValidation.isValid) {\n    errors.push(fileSizeValidation.error!);\n  }\n\n  // Scan file content (only if basic validations pass)\n  if (errors.length === 0) {\n    const contentScan = await scanFileContent(file);\n    warnings.push(...contentScan.warnings);\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n    warnings,\n    fileInfo\n  };\n}\n\n/**\n * Sanitizes filename for safe storage\n */\nexport function sanitizeFileName(fileName: string): string {\n  // Remove path components\n  const baseName = fileName.split(/[/\\\\]/).pop() || 'unnamed';\n\n  // Replace unsafe characters\n  const sanitized = baseName\n    .replace(/[^\\w\\s.-]/g, '_') // Replace non-alphanumeric chars except spaces, dots, hyphens\n    .replace(/\\s+/g, '_') // Replace spaces with underscores\n    .replace(/_{2,}/g, '_') // Replace multiple underscores with single\n    .replace(/^[._-]+|[._-]+$/g, ''); // Remove leading/trailing special chars\n\n  // Ensure we have a valid filename\n  if (!sanitized || sanitized.length === 0) {\n    return 'unnamed_file';\n  }\n\n  // Truncate if too long (keep extension)\n  if (sanitized.length > 100) {\n    const parts = sanitized.split('.');\n    const ext = parts.pop();\n    const name = parts.join('.').substring(0, 90);\n    return ext ? `${name}.${ext}` : name;\n  }\n\n  return sanitized;\n}\n\n/**\n * Rate limiting for file uploads\n */\nexport class UploadRateLimit {\n  private attempts: Map<string, number[]> = new Map();\n  private readonly maxAttempts: number;\n  private readonly windowMs: number;\n\n  constructor(maxAttempts = 5, windowMs = 15 * 60 * 1000) { // 5 attempts per 15 minutes\n    this.maxAttempts = maxAttempts;\n    this.windowMs = windowMs;\n  }\n\n  isAllowed(identifier: string): boolean {\n    const now = Date.now();\n    const attempts = this.attempts.get(identifier) || [];\n\n    // Remove old attempts outside the window\n    const recentAttempts = attempts.filter(time => now - time < this.windowMs);\n\n    // Update the attempts list\n    this.attempts.set(identifier, recentAttempts);\n\n    return recentAttempts.length < this.maxAttempts;\n  }\n\n  recordAttempt(identifier: string): void {\n    const now = Date.now();\n    const attempts = this.attempts.get(identifier) || [];\n    attempts.push(now);\n    this.attempts.set(identifier, attempts);\n  }\n\n  getRemainingAttempts(identifier: string): number {\n    const now = Date.now();\n    const attempts = this.attempts.get(identifier) || [];\n    const recentAttempts = attempts.filter(time => now - time < this.windowMs);\n    return Math.max(0, this.maxAttempts - recentAttempts.length);\n  }\n\n  getResetTime(identifier: string): number {\n    const attempts = this.attempts.get(identifier) || [];\n    if (attempts.length === 0) return 0;\n\n    const oldestAttempt = Math.min(...attempts);\n    return oldestAttempt + this.windowMs;\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,uBAAuB;;;;;;;;;;;;;;AAChB,MAAM,qBAAqB;IAChC,mBAAmB;QAAC;KAAO;IAC3B,sBAAsB;QAAC;KAAO;IAC9B,2EAA2E;QAAC;KAAQ;AACtF;AAEO,MAAM,qBAAqB;IAAC;IAAQ;IAAQ;CAAQ;AAGpD,MAAM,gBAAgB,IAAI,OAAO,MAAM,eAAe;AACtD,MAAM,gBAAgB,MAAM,cAAc;AAEjD,gDAAgD;AAChD,MAAM,qBAAqB;IACzB,4BAA4B;IAC5B;IACA;IACA;IACA;IACA;IAEA,sBAAsB;IACtB;IACA;IAEA,oCAAoC;IACpC;IAEA,0CAA0C;IAC1C;IACA;CACD;AAiBM,SAAS,iBAAiB,IAAU;IACzC,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IAExD,gCAAgC;IAChC,IAAI,CAAC,mBAAmB,QAAQ,CAAC,gBAAuB;QACtD,OAAO;YACL,SAAS;YACT,OAAO,CAAC,0CAA0C,EAAE,mBAAmB,IAAI,CAAC,OAAO;QACrF;IACF;IAEA,kBAAkB;IAClB,IAAI,CAAC,OAAO,IAAI,CAAC,oBAAoB,QAAQ,CAAC,KAAK,IAAI,GAAG;QACxD,OAAO;YACL,SAAS;YACT,OAAO,CAAC,6BAA6B,EAAE,OAAO,IAAI,CAAC,oBAAoB,IAAI,CAAC,OAAO;QACrF;IACF;IAEA,uCAAuC;IACvC,MAAM,oBAAoB,kBAAkB,CAAC,KAAK,IAAI,CAAoC;IAC1F,IAAI,CAAC,kBAAkB,QAAQ,CAAC,gBAAuB;QACrD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS,iBAAiB,IAAU;IACzC,IAAI,KAAK,IAAI,GAAG,eAAe;QAC7B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,mCAAmC,EAAE,KAAK,KAAK,CAAC,gBAAgB,CAAC,OAAO,IAAI,GAAG,EAAE,CAAC;QAC5F;IACF;IAEA,IAAI,KAAK,IAAI,GAAG,eAAe;QAC7B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,wCAAwC,EAAE,cAAc,MAAM,CAAC;QACzE;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS,iBAAiB,QAAgB;IAC/C,uBAAuB;IACvB,IAAI,SAAS,QAAQ,CAAC,OAAO;QAC3B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,2BAA2B;IAC3B,IAAI,SAAS,QAAQ,CAAC,SAAS,SAAS,QAAQ,CAAC,QAAQ,SAAS,QAAQ,CAAC,OAAO;QAChF,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,+BAA+B;IAC/B,IAAI,uBAAuB,IAAI,CAAC,WAAW;QACzC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QAAC;QAAO;QAAO;QAAO;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAClM,MAAM,iBAAiB,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW;IACzD,IAAI,cAAc,QAAQ,CAAC,iBAAiB;QAC1C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,eAAe;IACf,IAAI,SAAS,MAAM,GAAG,KAAK;QACzB,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,eAAe,gBAAgB,IAAU;IAC9C,MAAM,WAAqB,EAAE;IAE7B,IAAI;QACF,6CAA6C;QAC7C,MAAM,eAAe,MAAM,KAAK,KAAK,CAAC,GAAG,MAAM,WAAW;QAC1D,MAAM,aAAa,IAAI,YAAY,SAAS;YAAE,OAAO;QAAM,GAAG,MAAM,CAAC;QAErE,+BAA+B;QAC/B,KAAK,MAAM,WAAW,mBAAoB;YACxC,IAAI,QAAQ,IAAI,CAAC,aAAa;gBAC5B,SAAS,IAAI,CAAC;gBACd;YACF;QACF;QAEA,kDAAkD;QAClD,IAAI,KAAK,IAAI,KAAK,qBAAqB,WAAW,QAAQ,CAAC,OAAO;YAChE,SAAS,IAAI,CAAC;QAChB;QAEA,uCAAuC;QACvC,IAAI,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;YACzF,4FAA4F;YAC5F,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,QAAQ;gBAC9D,SAAS,IAAI,CAAC;YAChB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO;QACL,SAAS,SAAS,MAAM,KAAK;QAC7B;IACF;AACF;AAKO,eAAe,aAAa,IAAU;IAC3C,MAAM,SAAmB,EAAE;IAC3B,MAAM,WAAqB,EAAE;IAE7B,kBAAkB;IAClB,MAAM,WAAW;QACf,MAAM,KAAK,IAAI;QACf,MAAM,KAAK,IAAI;QACf,MAAM,KAAK,IAAI;QACf,WAAW,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;IAChE;IAEA,oBAAoB;IACpB,MAAM,qBAAqB,iBAAiB,KAAK,IAAI;IACrD,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAC/B,OAAO,IAAI,CAAC,mBAAmB,KAAK;IACtC;IAEA,qBAAqB;IACrB,MAAM,qBAAqB,iBAAiB;IAC5C,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAC/B,OAAO,IAAI,CAAC,mBAAmB,KAAK;IACtC;IAEA,qBAAqB;IACrB,MAAM,qBAAqB,iBAAiB;IAC5C,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAC/B,OAAO,IAAI,CAAC,mBAAmB,KAAK;IACtC;IAEA,qDAAqD;IACrD,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,MAAM,cAAc,MAAM,gBAAgB;QAC1C,SAAS,IAAI,IAAI,YAAY,QAAQ;IACvC;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;QACA;IACF;AACF;AAKO,SAAS,iBAAiB,QAAgB;IAC/C,yBAAyB;IACzB,MAAM,WAAW,SAAS,KAAK,CAAC,SAAS,GAAG,MAAM;IAElD,4BAA4B;IAC5B,MAAM,YAAY,SACf,OAAO,CAAC,cAAc,KAAK,8DAA8D;KACzF,OAAO,CAAC,QAAQ,KAAK,kCAAkC;KACvD,OAAO,CAAC,UAAU,KAAK,2CAA2C;KAClE,OAAO,CAAC,oBAAoB,KAAK,wCAAwC;IAE5E,kCAAkC;IAClC,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACxC,OAAO;IACT;IAEA,wCAAwC;IACxC,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,MAAM,QAAQ,UAAU,KAAK,CAAC;QAC9B,MAAM,MAAM,MAAM,GAAG;QACrB,MAAM,OAAO,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,GAAG;QAC1C,OAAO,MAAM,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG;IAClC;IAEA,OAAO;AACT;AAKO,MAAM;IACH,WAAkC,IAAI,MAAM;IACnC,YAAoB;IACpB,SAAiB;IAElC,YAAY,cAAc,CAAC,EAAE,WAAW,KAAK,KAAK,IAAI,CAAE;QACtD,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,UAAU,UAAkB,EAAW;QACrC,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,EAAE;QAEpD,yCAAyC;QACzC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,OAAQ,MAAM,OAAO,IAAI,CAAC,QAAQ;QAEzE,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY;QAE9B,OAAO,eAAe,MAAM,GAAG,IAAI,CAAC,WAAW;IACjD;IAEA,cAAc,UAAkB,EAAQ;QACtC,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,EAAE;QACpD,SAAS,IAAI,CAAC;QACd,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY;IAChC;IAEA,qBAAqB,UAAkB,EAAU;QAC/C,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,EAAE;QACpD,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,OAAQ,MAAM,OAAO,IAAI,CAAC,QAAQ;QACzE,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,eAAe,MAAM;IAC7D;IAEA,aAAa,UAAkB,EAAU;QACvC,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,EAAE;QACpD,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;QAElC,MAAM,gBAAgB,KAAK,GAAG,IAAI;QAClC,OAAO,gBAAgB,IAAI,CAAC,QAAQ;IACtC;AACF", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cvmatic/src/components/upload/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Progress } from '@/components/ui/progress';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Upload,\n  FileText,\n  X,\n  CheckCircle,\n  AlertCircle,\n  Loader2,\n  Shield,\n  AlertTriangle\n} from 'lucide-react';\nimport { validateFile, sanitizeFileName, UploadRateLimit } from '@/lib/security';\n\ninterface FileUploadProps {\n  onFileUpload?: (file: File) => Promise<void>;\n  isRTL?: boolean;\n  maxSize?: number; // in bytes\n  acceptedFormats?: string[];\n}\n\ninterface UploadedFile {\n  file: File;\n  progress: number;\n  status: 'uploading' | 'success' | 'error' | 'validating';\n  error?: string;\n  warnings?: string[];\n  sanitizedName?: string;\n}\n\nexport default function FileUpload({\n  onFileUpload,\n  isRTL = false,\n  maxSize = 4 * 1024 * 1024, // 4MB\n  acceptedFormats = ['.pdf', '.doc', '.docx']\n}: FileUploadProps) {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isDragActive, setIsDragActive] = useState(false);\n  const [rateLimit] = useState(() => new UploadRateLimit());\n\n  const getClientIdentifier = (): string => {\n    // In a real app, you'd use IP address or user ID\n    // For demo purposes, we'll use a browser fingerprint\n    return 'demo-client-' + (typeof window !== 'undefined' ? window.navigator.userAgent.slice(0, 20) : 'server');\n  };\n\n  const simulateUpload = async (file: File): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += Math.random() * 30;\n        if (progress >= 100) {\n          progress = 100;\n          clearInterval(interval);\n\n          // Simulate random success/failure for demo\n          if (Math.random() > 0.1) { // 90% success rate\n            resolve();\n          } else {\n            reject(new Error(isRTL ? 'שגיאה בהעלאת הקובץ' : 'Upload failed'));\n          }\n        }\n\n        setUploadedFiles(prev =>\n          prev.map(f =>\n            f.file === file\n              ? { ...f, progress }\n              : f\n          )\n        );\n      }, 200);\n    });\n  };\n\n  const handleFileUpload = async (file: File) => {\n    const clientId = getClientIdentifier();\n\n    // Check rate limiting\n    if (!rateLimit.isAllowed(clientId)) {\n      const resetTime = new Date(rateLimit.getResetTime(clientId));\n      const error = isRTL\n        ? `חרגת ממגבלת ההעלאות. נסה שוב ב-${resetTime.toLocaleTimeString()}`\n        : `Upload limit exceeded. Try again at ${resetTime.toLocaleTimeString()}`;\n\n      setUploadedFiles(prev => [...prev, {\n        file,\n        progress: 0,\n        status: 'error',\n        error\n      }]);\n      return;\n    }\n\n    // Record the attempt\n    rateLimit.recordAttempt(clientId);\n\n    // Add file to validation queue\n    setUploadedFiles(prev => [...prev, {\n      file,\n      progress: 0,\n      status: 'validating'\n    }]);\n\n    try {\n      // Validate file with security checks\n      const validation = await validateFile(file);\n\n      if (!validation.isValid) {\n        setUploadedFiles(prev =>\n          prev.map(f =>\n            f.file === file\n              ? {\n                ...f,\n                status: 'error',\n                error: validation.errors.join(', ')\n              }\n              : f\n          )\n        );\n        return;\n      }\n\n      // Sanitize filename\n      const sanitizedName = sanitizeFileName(file.name);\n\n      // Update to uploading status with warnings if any\n      setUploadedFiles(prev =>\n        prev.map(f =>\n          f.file === file\n            ? {\n              ...f,\n              status: 'uploading',\n              warnings: validation.warnings,\n              sanitizedName\n            }\n            : f\n        )\n      );\n\n      // Proceed with upload\n      if (onFileUpload) {\n        await onFileUpload(file);\n      } else {\n        await simulateUpload(file);\n      }\n\n      setUploadedFiles(prev =>\n        prev.map(f =>\n          f.file === file\n            ? { ...f, status: 'success', progress: 100 }\n            : f\n        )\n      );\n    } catch (err) {\n      setUploadedFiles(prev =>\n        prev.map(f =>\n          f.file === file\n            ? {\n              ...f,\n              status: 'error',\n              error: err instanceof Error ? err.message : 'Upload failed'\n            }\n            : f\n        )\n      );\n    }\n  };\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    acceptedFiles.forEach(handleFileUpload);\n  }, [onFileUpload]);\n\n  const { getRootProps, getInputProps, isDragActive: dropzoneActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf'],\n      'application/msword': ['.doc'],\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']\n    },\n    maxSize,\n    multiple: false,\n    onDragEnter: () => setIsDragActive(true),\n    onDragLeave: () => setIsDragActive(false),\n    onDropAccepted: () => setIsDragActive(false),\n    onDropRejected: () => setIsDragActive(false)\n  });\n\n  const removeFile = (fileToRemove: File) => {\n    setUploadedFiles(prev => prev.filter(f => f.file !== fileToRemove));\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"w-full max-w-2xl mx-auto space-y-6\">\n      {/* Upload Area */}\n      <Card className=\"border-2 border-dashed transition-colors duration-200 hover:border-primary/50\">\n        <CardContent className=\"p-8\">\n          <div\n            {...getRootProps()}\n            className={`\n              text-center cursor-pointer transition-all duration-200\n              ${(isDragActive || dropzoneActive) ? 'scale-105' : ''}\n            `}\n          >\n            <input {...getInputProps()} />\n\n            <div className=\"flex flex-col items-center space-y-4\">\n              <div className={`\n                p-4 rounded-full transition-colors duration-200\n                ${(isDragActive || dropzoneActive)\n                  ? 'bg-primary/10 text-primary'\n                  : 'bg-muted text-muted-foreground'\n                }\n              `}>\n                <Upload className=\"h-8 w-8\" />\n              </div>\n\n              <div className=\"space-y-2\">\n                <h3 className=\"text-lg font-semibold\">\n                  {isRTL ? 'העלה את קובץ הקו״ח שלך' : 'Upload Your Resume'}\n                </h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  {isRTL\n                    ? 'גרור ושחרר או לחץ לבחירת קובץ'\n                    : 'Drag and drop or click to select a file'\n                  }\n                </p>\n                <p className=\"text-xs text-muted-foreground\">\n                  {isRTL\n                    ? `פורמטים נתמכים: ${acceptedFormats.join(', ')} • מקסימום ${Math.round(maxSize / (1024 * 1024))}MB`\n                    : `Supported formats: ${acceptedFormats.join(', ')} • Max ${Math.round(maxSize / (1024 * 1024))}MB`\n                  }\n                </p>\n              </div>\n\n              <Button variant=\"outline\" className=\"mt-4\">\n                {isRTL ? 'בחר קובץ' : 'Choose File'}\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Uploaded Files */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"space-y-3\">\n          <h4 className=\"text-sm font-medium\">\n            {isRTL ? 'קבצים שהועלו' : 'Uploaded Files'}\n          </h4>\n\n          {uploadedFiles.map((uploadedFile, index) => (\n            <Card key={index} className=\"p-4\">\n              <div className=\"flex items-center space-x-3\">\n                <FileText className=\"h-8 w-8 text-primary flex-shrink-0\" />\n\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium truncate\">\n                        {uploadedFile.file.name}\n                      </p>\n                      {uploadedFile.sanitizedName && uploadedFile.sanitizedName !== uploadedFile.file.name && (\n                        <p className=\"text-xs text-muted-foreground\">\n                          {isRTL ? 'שם מנוקה:' : 'Sanitized:'} {uploadedFile.sanitizedName}\n                        </p>\n                      )}\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      {uploadedFile.status === 'validating' && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          <Shield className=\"h-3 w-3 mr-1\" />\n                          {isRTL ? 'בודק' : 'Validating'}\n                        </Badge>\n                      )}\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={() => removeFile(uploadedFile.file)}\n                        className=\"flex-shrink-0\"\n                      >\n                        <X className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n\n                  <p className=\"text-xs text-muted-foreground\">\n                    {formatFileSize(uploadedFile.file.size)}\n                  </p>\n\n                  {uploadedFile.status === 'validating' && (\n                    <div className=\"mt-2 flex items-center space-x-2 text-blue-600\">\n                      <Shield className=\"h-4 w-4 animate-pulse\" />\n                      <span className=\"text-xs\">\n                        {isRTL ? 'בודק אבטחה...' : 'Security validation...'}\n                      </span>\n                    </div>\n                  )}\n\n                  {uploadedFile.status === 'uploading' && (\n                    <div className=\"mt-2 space-y-1\">\n                      <Progress value={uploadedFile.progress} className=\"h-2\" />\n                      <div className=\"flex items-center space-x-2\">\n                        <Loader2 className=\"h-3 w-3 animate-spin\" />\n                        <span className=\"text-xs text-muted-foreground\">\n                          {isRTL ? 'מעלה...' : 'Uploading...'}\n                        </span>\n                      </div>\n                    </div>\n                  )}\n\n                  {uploadedFile.status === 'success' && (\n                    <div className=\"mt-2 space-y-2\">\n                      <div className=\"flex items-center space-x-2 text-green-600\">\n                        <CheckCircle className=\"h-4 w-4\" />\n                        <span className=\"text-xs\">\n                          {isRTL ? 'הועלה בהצלחה' : 'Upload successful'}\n                        </span>\n                      </div>\n\n                      {uploadedFile.warnings && uploadedFile.warnings.length > 0 && (\n                        <Alert className=\"border-yellow-200 bg-yellow-50\">\n                          <AlertTriangle className=\"h-4 w-4 text-yellow-600\" />\n                          <AlertDescription className=\"text-xs\">\n                            <div className=\"font-medium mb-1\">\n                              {isRTL ? 'אזהרות אבטחה:' : 'Security warnings:'}\n                            </div>\n                            <ul className=\"list-disc list-inside space-y-1\">\n                              {uploadedFile.warnings.map((warning, idx) => (\n                                <li key={idx}>{warning}</li>\n                              ))}\n                            </ul>\n                          </AlertDescription>\n                        </Alert>\n                      )}\n                    </div>\n                  )}\n\n                  {uploadedFile.status === 'error' && (\n                    <Alert className=\"mt-2 border-red-200 bg-red-50\">\n                      <AlertCircle className=\"h-4 w-4 text-red-600\" />\n                      <AlertDescription className=\"text-xs\">\n                        {uploadedFile.error}\n                      </AlertDescription>\n                    </Alert>\n                  )}\n                </div>\n              </div>\n            </Card>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAnBA;;;;;;;;;;AAqCe,SAAS,WAAW,EACjC,YAAY,EACZ,QAAQ,KAAK,EACb,UAAU,IAAI,OAAO,IAAI,EACzB,kBAAkB;IAAC;IAAQ;IAAQ;CAAQ,EAC3B;;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;+BAAE,IAAM,IAAI,yHAAA,CAAA,kBAAe;;IAEtD,MAAM,sBAAsB;QAC1B,iDAAiD;QACjD,qDAAqD;QACrD,OAAO,iBAAiB,CAAC,uCAAgC,OAAO,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,0CAAc;IAC7G;IAEA,MAAM,iBAAiB,OAAO;QAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,WAAW;YACf,MAAM,WAAW,YAAY;gBAC3B,YAAY,KAAK,MAAM,KAAK;gBAC5B,IAAI,YAAY,KAAK;oBACnB,WAAW;oBACX,cAAc;oBAEd,2CAA2C;oBAC3C,IAAI,KAAK,MAAM,KAAK,KAAK;wBACvB;oBACF,OAAO;wBACL,OAAO,IAAI,MAAM,QAAQ,uBAAuB;oBAClD;gBACF;gBAEA,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,IAAI,KAAK,OACP;4BAAE,GAAG,CAAC;4BAAE;wBAAS,IACjB;YAGV,GAAG;QACL;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,WAAW;QAEjB,sBAAsB;QACtB,IAAI,CAAC,UAAU,SAAS,CAAC,WAAW;YAClC,MAAM,YAAY,IAAI,KAAK,UAAU,YAAY,CAAC;YAClD,MAAM,QAAQ,QACV,CAAC,+BAA+B,EAAE,UAAU,kBAAkB,IAAI,GAClE,CAAC,oCAAoC,EAAE,UAAU,kBAAkB,IAAI;YAE3E,iBAAiB,CAAA,OAAQ;uBAAI;oBAAM;wBACjC;wBACA,UAAU;wBACV,QAAQ;wBACR;oBACF;iBAAE;YACF;QACF;QAEA,qBAAqB;QACrB,UAAU,aAAa,CAAC;QAExB,+BAA+B;QAC/B,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;oBACjC;oBACA,UAAU;oBACV,QAAQ;gBACV;aAAE;QAEF,IAAI;YACF,qCAAqC;YACrC,MAAM,aAAa,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE;YAEtC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,IAAI,KAAK,OACP;4BACA,GAAG,CAAC;4BACJ,QAAQ;4BACR,OAAO,WAAW,MAAM,CAAC,IAAI,CAAC;wBAChC,IACE;gBAGR;YACF;YAEA,oBAAoB;YACpB,MAAM,gBAAgB,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI;YAEhD,kDAAkD;YAClD,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,IAAI,KAAK,OACP;wBACA,GAAG,CAAC;wBACJ,QAAQ;wBACR,UAAU,WAAW,QAAQ;wBAC7B;oBACF,IACE;YAIR,sBAAsB;YACtB,IAAI,cAAc;gBAChB,MAAM,aAAa;YACrB,OAAO;gBACL,MAAM,eAAe;YACvB;YAEA,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,IAAI,KAAK,OACP;wBAAE,GAAG,CAAC;wBAAE,QAAQ;wBAAW,UAAU;oBAAI,IACzC;QAGV,EAAE,OAAO,KAAK;YACZ,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,IAAI,KAAK,OACP;wBACA,GAAG,CAAC;wBACJ,QAAQ;wBACR,OAAO,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAC9C,IACE;QAGV;IACF;IAEA,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC;YAC1B,cAAc,OAAO,CAAC;QACxB;yCAAG;QAAC;KAAa;IAEjB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,cAAc,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChF;QACA,QAAQ;YACN,mBAAmB;gBAAC;aAAO;YAC3B,sBAAsB;gBAAC;aAAO;YAC9B,2EAA2E;gBAAC;aAAQ;QACtF;QACA;QACA,UAAU;QACV,WAAW;sCAAE,IAAM,gBAAgB;;QACnC,WAAW;sCAAE,IAAM,gBAAgB;;QACnC,cAAc;sCAAE,IAAM,gBAAgB;;QACtC,cAAc;sCAAE,IAAM,gBAAgB;;IACxC;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IACvD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBACE,GAAG,cAAc;wBAClB,WAAW,CAAC;;cAEV,EAAE,AAAC,gBAAgB,iBAAkB,cAAc,GAAG;YACxD,CAAC;;0CAED,6LAAC;gCAAO,GAAG,eAAe;;;;;;0CAE1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC;;gBAEf,EAAE,AAAC,gBAAgB,iBACf,+BACA,iCACH;cACH,CAAC;kDACC,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,QAAQ,2BAA2B;;;;;;0DAEtC,6LAAC;gDAAE,WAAU;0DACV,QACG,kCACA;;;;;;0DAGN,6LAAC;gDAAE,WAAU;0DACV,QACG,CAAC,gBAAgB,EAAE,gBAAgB,IAAI,CAAC,MAAM,WAAW,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,EAAE,CAAC,GAClG,CAAC,mBAAmB,EAAE,gBAAgB,IAAI,CAAC,MAAM,OAAO,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,EAAE,CAAC;;;;;;;;;;;;kDAKzG,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;kDACjC,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ/B,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,QAAQ,iBAAiB;;;;;;oBAG3B,cAAc,GAAG,CAAC,CAAC,cAAc,sBAChC,6LAAC,mIAAA,CAAA,OAAI;4BAAa,WAAU;sCAC1B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDAEpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EACV,aAAa,IAAI,CAAC,IAAI;;;;;;4DAExB,aAAa,aAAa,IAAI,aAAa,aAAa,KAAK,aAAa,IAAI,CAAC,IAAI,kBAClF,6LAAC;gEAAE,WAAU;;oEACV,QAAQ,cAAc;oEAAa;oEAAE,aAAa,aAAa;;;;;;;;;;;;;kEAItE,6LAAC;wDAAI,WAAU;;4DACZ,aAAa,MAAM,KAAK,8BACvB,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;kFACjC,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEACjB,QAAQ,SAAS;;;;;;;0EAGtB,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,WAAW,aAAa,IAAI;gEAC3C,WAAU;0EAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAKnB,6LAAC;gDAAE,WAAU;0DACV,eAAe,aAAa,IAAI,CAAC,IAAI;;;;;;4CAGvC,aAAa,MAAM,KAAK,8BACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEACb,QAAQ,kBAAkB;;;;;;;;;;;;4CAKhC,aAAa,MAAM,KAAK,6BACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,OAAO,aAAa,QAAQ;wDAAE,WAAU;;;;;;kEAClD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;gEAAK,WAAU;0EACb,QAAQ,YAAY;;;;;;;;;;;;;;;;;;4CAM5B,aAAa,MAAM,KAAK,2BACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;gEAAK,WAAU;0EACb,QAAQ,iBAAiB;;;;;;;;;;;;oDAI7B,aAAa,QAAQ,IAAI,aAAa,QAAQ,CAAC,MAAM,GAAG,mBACvD,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;;0EACf,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;0EACzB,6LAAC,oIAAA,CAAA,mBAAgB;gEAAC,WAAU;;kFAC1B,6LAAC;wEAAI,WAAU;kFACZ,QAAQ,kBAAkB;;;;;;kFAE7B,6LAAC;wEAAG,WAAU;kFACX,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBACnC,6LAAC;0FAAc;+EAAN;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAStB,aAAa,MAAM,KAAK,yBACvB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC,oIAAA,CAAA,mBAAgB;wDAAC,WAAU;kEACzB,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;2BA1FpB;;;;;;;;;;;;;;;;;AAsGvB;GA1UwB;;QA8IgD,2KAAA,CAAA,cAAW;;;KA9I3D", "debugId": null}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/cvmatic/src/app/upload/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { \n  Upload, \n  FileText, \n  CheckCircle, \n  AlertCircle,\n  Brain,\n  Target,\n  Zap,\n  Shield,\n  ArrowRight,\n  BarChart3\n} from 'lucide-react';\nimport FileUpload from '@/components/upload/FileUpload';\n\nexport default function UploadPage() {\n  const [analysisStep, setAnalysisStep] = useState<'upload' | 'analyzing' | 'complete'>('upload');\n  const [analysisProgress, setAnalysisProgress] = useState(0);\n\n  const handleFileUpload = async (file: File) => {\n    setAnalysisStep('analyzing');\n    \n    // Simulate analysis progress\n    const interval = setInterval(() => {\n      setAnalysisProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(interval);\n          setAnalysisStep('complete');\n          return 100;\n        }\n        return prev + Math.random() * 15;\n      });\n    }, 300);\n  };\n\n  const features = [\n    {\n      icon: Brain,\n      title: 'AI Content Analysis',\n      description: 'Deep analysis of your resume content, skills, and experience relevance.'\n    },\n    {\n      icon: Target,\n      title: 'ATS Optimization',\n      description: 'Ensure your resume passes Applicant Tracking Systems with flying colors.'\n    },\n    {\n      icon: Zap,\n      title: 'Instant Feedback',\n      description: 'Get immediate insights and recommendations for improvement.'\n    },\n    {\n      icon: Shield,\n      title: 'Privacy Protected',\n      description: 'Your data is secure and never stored permanently on our servers.'\n    }\n  ];\n\n  const analysisSteps = [\n    'Parsing document structure...',\n    'Analyzing content quality...',\n    'Checking ATS compatibility...',\n    'Evaluating keyword density...',\n    'Assessing formatting...',\n    'Generating recommendations...',\n    'Calculating final score...'\n  ];\n\n  return (\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Header */}\n        <div className=\"text-center space-y-4 mb-12\">\n          <Badge variant=\"secondary\" className=\"w-fit mx-auto\">\n            <Upload className=\"h-3 w-3 mr-1\" />\n            Resume Analysis\n          </Badge>\n          <h1 className=\"text-3xl lg:text-5xl font-bold\">\n            Upload Your Resume\n          </h1>\n          <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n            Get comprehensive AI-powered analysis of your resume with detailed feedback, \n            scoring, and actionable recommendations for improvement.\n          </p>\n        </div>\n\n        {analysisStep === 'upload' && (\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Upload Section */}\n            <div className=\"space-y-8\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <FileText className=\"h-5 w-5\" />\n                    Upload Resume\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <FileUpload onFileUpload={handleFileUpload} />\n                </CardContent>\n              </Card>\n\n              {/* Requirements */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Requirements</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-3\">\n                  <div className=\"flex items-center gap-2 text-sm\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                    <span>PDF, DOC, or DOCX format</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 text-sm\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                    <span>Maximum file size: 4MB</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 text-sm\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                    <span>Text-based content (not scanned images)</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 text-sm\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                    <span>English or Hebrew language</span>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Features Section */}\n            <div className=\"space-y-6\">\n              <h3 className=\"text-2xl font-semibold\">What You'll Get</h3>\n              \n              <div className=\"space-y-4\">\n                {features.map((feature, index) => {\n                  const Icon = feature.icon;\n                  return (\n                    <Card key={index} className=\"p-4\">\n                      <div className=\"flex items-start gap-4\">\n                        <div className=\"bg-primary/10 text-primary rounded-lg p-2 flex-shrink-0\">\n                          <Icon className=\"h-5 w-5\" />\n                        </div>\n                        <div>\n                          <h4 className=\"font-semibold mb-1\">{feature.title}</h4>\n                          <p className=\"text-sm text-muted-foreground\">{feature.description}</p>\n                        </div>\n                      </div>\n                    </Card>\n                  );\n                })}\n              </div>\n\n              {/* Sample Score */}\n              <Card className=\"p-6 bg-gradient-to-br from-primary/5 to-accent/5\">\n                <div className=\"text-center space-y-4\">\n                  <div className=\"text-4xl font-bold text-primary\">85/100</div>\n                  <div className=\"text-sm text-muted-foreground\">Sample Score</div>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between text-sm\">\n                      <span>Content Quality</span>\n                      <span className=\"font-medium\">92%</span>\n                    </div>\n                    <Progress value={92} className=\"h-2\" />\n                    <div className=\"flex justify-between text-sm\">\n                      <span>ATS Compatibility</span>\n                      <span className=\"font-medium\">78%</span>\n                    </div>\n                    <Progress value={78} className=\"h-2\" />\n                    <div className=\"flex justify-between text-sm\">\n                      <span>Formatting</span>\n                      <span className=\"font-medium\">85%</span>\n                    </div>\n                    <Progress value={85} className=\"h-2\" />\n                  </div>\n                </div>\n              </Card>\n            </div>\n          </div>\n        )}\n\n        {analysisStep === 'analyzing' && (\n          <div className=\"max-w-2xl mx-auto\">\n            <Card className=\"p-8\">\n              <div className=\"text-center space-y-6\">\n                <div className=\"bg-primary/10 text-primary rounded-full p-4 w-fit mx-auto\">\n                  <Brain className=\"h-8 w-8 animate-pulse\" />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <h2 className=\"text-2xl font-bold\">Analyzing Your Resume</h2>\n                  <p className=\"text-muted-foreground\">\n                    Our AI is carefully reviewing your resume. This usually takes 30-60 seconds.\n                  </p>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <Progress value={analysisProgress} className=\"h-3\" />\n                  <div className=\"text-sm text-muted-foreground\">\n                    {Math.round(analysisProgress)}% Complete\n                  </div>\n                </div>\n\n                <div className=\"text-left space-y-2 max-w-sm mx-auto\">\n                  {analysisSteps.map((step, index) => {\n                    const isActive = index < (analysisProgress / 100) * analysisSteps.length;\n                    const isCompleted = index < ((analysisProgress / 100) * analysisSteps.length) - 1;\n                    \n                    return (\n                      <div key={index} className={`flex items-center gap-2 text-sm transition-colors ${\n                        isCompleted ? 'text-green-600' : isActive ? 'text-primary' : 'text-muted-foreground'\n                      }`}>\n                        {isCompleted ? (\n                          <CheckCircle className=\"h-4 w-4\" />\n                        ) : isActive ? (\n                          <div className=\"h-4 w-4 border-2 border-primary border-t-transparent rounded-full animate-spin\" />\n                        ) : (\n                          <div className=\"h-4 w-4 border-2 border-muted rounded-full\" />\n                        )}\n                        <span>{step}</span>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </Card>\n          </div>\n        )}\n\n        {analysisStep === 'complete' && (\n          <div className=\"max-w-2xl mx-auto\">\n            <Card className=\"p-8\">\n              <div className=\"text-center space-y-6\">\n                <div className=\"bg-green-100 text-green-600 rounded-full p-4 w-fit mx-auto\">\n                  <CheckCircle className=\"h-8 w-8\" />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <h2 className=\"text-2xl font-bold\">Analysis Complete!</h2>\n                  <p className=\"text-muted-foreground\">\n                    Your resume has been successfully analyzed. View your detailed results below.\n                  </p>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-primary/5 to-accent/5 rounded-lg p-6\">\n                  <div className=\"text-4xl font-bold text-primary mb-2\">87/100</div>\n                  <div className=\"text-sm text-muted-foreground mb-4\">Overall Score</div>\n                  <Badge variant=\"secondary\" className=\"bg-green-100 text-green-700\">\n                    Strong Resume\n                  </Badge>\n                </div>\n\n                <div className=\"flex flex-col sm:flex-row gap-4\">\n                  <Button size=\"lg\" className=\"cvmatic-button-primary\">\n                    <BarChart3 className=\"h-5 w-5 mr-2\" />\n                    View Detailed Results\n                  </Button>\n                  <Button variant=\"outline\" size=\"lg\">\n                    Download Report\n                    <ArrowRight className=\"h-5 w-5 ml-2\" />\n                  </Button>\n                </div>\n              </div>\n            </Card>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAnBA;;;;;;;;AAqBe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IACtF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAEhB,6BAA6B;QAC7B,MAAM,WAAW,YAAY;YAC3B,oBAAoB,CAAA;gBAClB,IAAI,QAAQ,KAAK;oBACf,cAAc;oBACd,gBAAgB;oBAChB,OAAO;gBACT;gBACA,OAAO,OAAO,KAAK,MAAM,KAAK;YAChC;QACF,GAAG;IACL;IAEA,MAAM,WAAW;QACf;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;;8CACnC,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGrC,6LAAC;4BAAG,WAAU;sCAAiC;;;;;;sCAG/C,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;gBAMhE,iBAAiB,0BAChB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,6IAAA,CAAA,UAAU;gDAAC,cAAc;;;;;;;;;;;;;;;;;8CAK9B,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;;;;;;sDAEjC,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wCACtB,MAAM,OAAO,QAAQ,IAAI;wCACzB,qBACE,6LAAC,mIAAA,CAAA,OAAI;4CAAa,WAAU;sDAC1B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAsB,QAAQ,KAAK;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAiC,QAAQ,WAAW;;;;;;;;;;;;;;;;;;2CAP5D;;;;;oCAYf;;;;;;8CAIF,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAkC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,OAAO;wDAAI,WAAU;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,OAAO;wDAAI,WAAU;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,OAAO;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ1C,iBAAiB,6BAChB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAGnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,OAAO;4CAAkB,WAAU;;;;;;sDAC7C,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,KAAK,CAAC;gDAAkB;;;;;;;;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,MAAM;wCACxB,MAAM,WAAW,QAAQ,AAAC,mBAAmB,MAAO,cAAc,MAAM;wCACxE,MAAM,cAAc,QAAQ,AAAE,mBAAmB,MAAO,cAAc,MAAM,GAAI;wCAEhF,qBACE,6LAAC;4CAAgB,WAAW,CAAC,kDAAkD,EAC7E,cAAc,mBAAmB,WAAW,iBAAiB,yBAC7D;;gDACC,4BACC,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;2DACrB,yBACF,6LAAC;oDAAI,WAAU;;;;;yEAEf,6LAAC;oDAAI,WAAU;;;;;;8DAEjB,6LAAC;8DAAM;;;;;;;2CAVC;;;;;oCAad;;;;;;;;;;;;;;;;;;;;;;gBAOT,iBAAiB,4BAChB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAA8B;;;;;;;;;;;;8CAKrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;;8DAC1B,6LAAC,qNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;gDAAK;8DAElC,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C;GA5PwB;KAAA", "debugId": null}}]}