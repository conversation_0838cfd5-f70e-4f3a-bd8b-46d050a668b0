"""
Main CV Analysis Service
"""

from typing import Optional, Dict, Any
from datetime import datetime
import structlog

from app.core.config import settings
from app.core.exceptions import FileProcessingError, TextExtractionError, NLPProcessingError
from app.core.logging import CVAnalysisLogger
from app.extractors.text_extractor import TextExtractor
from app.nlp.multilingual_processor import MultilingualNLPProcessor
from app.ml.adaptive_classifier import AdaptiveSkillClassifier
from app.ml.cv_scorer import CVScorer
from app.llm.ollama_client import ollama_client, LLMAnalysisRequest
from app.models.cv_analysis import (
    CVAnalysisResult, CVContent, PersonalInfo, Skill, Experience,
    Education, Language, Certification, ScoreBreakdown, Recommendation,
    ExperienceLevelEnum
)

logger = CVAnalysisLogger("cv_analyzer")


class CVAnalyzer:
    """Main CV analysis service orchestrator"""
    
    def __init__(self):
        self.text_extractor = TextExtractor()
        self.nlp_processor = MultilingualNLPProcessor()
        self.adaptive_classifier = AdaptiveSkillClassifier()
        self.cv_scorer = CVScorer()
        
        self.initialized = False
    
    async def initialize(self):
        """Initialize all components"""
        if self.initialized:
            return
        
        try:
            # Initialize NLP processor
            await self.nlp_processor.initialize()
            
            # Initialize adaptive classifier
            await self.adaptive_classifier.initialize()
            
            self.initialized = True
            logger.logger.info("cv_analyzer_initialized")
            
        except Exception as e:
            logger.log_error(
                error_type="cv_analyzer_init_failed",
                error_message=str(e),
                context={}
            )
            raise
    
    async def analyze_cv(
        self,
        file_content: bytes,
        filename: str,
        target_position: Optional[str] = None,
        target_industry: Optional[str] = None,
        language: str = "auto",
    ) -> CVAnalysisResult:
        """
        Comprehensive CV analysis
        
        Args:
            file_content: CV file content as bytes
            filename: Original filename
            target_position: Target job position
            target_industry: Target industry
            language: CV language (auto, en, he)
            
        Returns:
            Complete CV analysis results
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # Step 1: Extract text from file
            logger.logger.info("starting_text_extraction", filename=filename)

            # Save file temporarily for processing
            import tempfile
            import os

            logger.logger.info("creating_temp_file", filename=filename)
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            logger.logger.info("temp_file_created", filename=filename, temp_path=temp_file_path)

            try:
                file_info = {"filename": filename, "size": len(file_content)}
                logger.logger.info("calling_extract_text", filename=filename, file_info=file_info)
                extraction_result = await self.text_extractor.extract_text(
                    temp_file_path, file_info
                )
                logger.logger.info("extract_text_returned", filename=filename, result_type=type(extraction_result).__name__)
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    logger.logger.info("temp_file_cleaned", filename=filename)

            logger.logger.info(
                "text_extraction_raw_result",
                filename=filename,
                extraction_result_type=type(extraction_result).__name__,
                extraction_result_keys=list(extraction_result.keys()) if isinstance(extraction_result, dict) else "not_dict"
            )

            # Extract values from the result dictionary
            extracted_text = extraction_result.get("cleaned_text", "")
            detected_language = extraction_result.get("language", "en")
            quality_score = extraction_result.get("quality_score", 0.0)

            if not extracted_text.strip():
                raise TextExtractionError("No text could be extracted from the file")
            
            logger.logger.info(
                "text_extraction_completed",
                filename=filename,
                text_length=len(extracted_text),
                detected_language=detected_language,
                quality_score=quality_score
            )
            
            # Step 2: Process with NLP
            logger.logger.info("starting_nlp_processing", filename=filename)
            
            # Use detected language if auto mode
            processing_language = detected_language if language == "auto" else language
            
            nlp_result = await self.nlp_processor.process_cv_text(
                text=extracted_text,
                language=processing_language
            )

            # Debug: Check the type and content of nlp_result
            logger.logger.info(
                "nlp_result_debug",
                filename=filename,
                nlp_result_type=type(nlp_result).__name__,
                nlp_result_content=str(nlp_result)[:200] if isinstance(nlp_result, str) else "dict"
            )

            # Ensure nlp_result is a dictionary
            if not isinstance(nlp_result, dict):
                raise NLPProcessingError(f"NLP processor returned {type(nlp_result).__name__} instead of dict: {str(nlp_result)[:100]}")

            logger.logger.info(
                "nlp_processing_completed",
                filename=filename,
                skills_found=len(nlp_result.get('skills', [])),
                experience_found=len(nlp_result.get('experience', [])),
                education_found=len(nlp_result.get('education', []))
            )
            
            # Step 3: Enhance skills with adaptive classification
            logger.logger.info("starting_skill_classification", filename=filename)
            
            enhanced_skills = []
            for skill_data in nlp_result.get('skills', []):
                # Classify skill with adaptive classifier
                category, confidence = await self.adaptive_classifier.classify_skill(
                    skill_text=skill_data['name'],
                    context=skill_data.get('context', '')
                )
                
                enhanced_skill = Skill(
                    name=skill_data['name'],
                    category=category,
                    confidence=confidence,
                    context=skill_data.get('context', ''),
                    years_experience=skill_data.get('years_experience'),
                )
                enhanced_skills.append(enhanced_skill)
            
            logger.logger.info(
                "skill_classification_completed",
                filename=filename,
                classified_skills=len(enhanced_skills)
            )
            
            # Step 4: Build CV content structure
            cv_content = CVContent(
                personal_info=PersonalInfo(
                    full_name=nlp_result.get('personal_info', {}).get('full_name'),
                    email=nlp_result.get('personal_info', {}).get('email'),
                    phone=nlp_result.get('personal_info', {}).get('phone'),
                    location=nlp_result.get('personal_info', {}).get('location'),
                    linkedin=nlp_result.get('personal_info', {}).get('linkedin'),
                    summary=nlp_result.get('personal_info', {}).get('summary'),
                ),
                skills=enhanced_skills,
                experience=[
                    Experience(
                        position=exp.get('position'),
                        company=exp.get('company'),
                        start_date=exp.get('start_date'),
                        end_date=exp.get('end_date'),
                        duration_months=exp.get('duration_months'),
                        description=exp.get('description'),
                        location=exp.get('location'),
                    )
                    for exp in nlp_result.get('experience', [])
                ],
                education=[
                    Education(
                        institution=edu.get('institution'),
                        degree=edu.get('degree'),
                        field_of_study=edu.get('field_of_study'),
                        start_date=edu.get('start_date'),
                        end_date=edu.get('end_date'),
                        gpa=edu.get('gpa'),
                        location=edu.get('location'),
                    )
                    for edu in nlp_result.get('education', [])
                ],
                languages=[
                    Language(
                        name=lang.get('name'),
                        proficiency=lang.get('proficiency'),
                        native=lang.get('native', False),
                    )
                    for lang in nlp_result.get('languages', [])
                ],
                certifications=[
                    Certification(
                        name=cert.get('name'),
                        issuer=cert.get('issuer'),
                        date_obtained=cert.get('date_obtained'),
                        expiry_date=cert.get('expiry_date'),
                        credential_id=cert.get('credential_id'),
                    )
                    for cert in nlp_result.get('certifications', [])
                ],
                raw_text=extracted_text,
                detected_language=detected_language,
                text_quality_score=quality_score,
            )
            
            # Step 5: Score CV and generate recommendations
            logger.logger.info("starting_cv_scoring", filename=filename)
            
            score_breakdown, recommendations, experience_level = await self.cv_scorer.score_cv(
                cv_content=cv_content,
                target_position=target_position,
                target_industry=target_industry,
            )
            
            logger.logger.info(
                "cv_scoring_completed",
                filename=filename,
                overall_score=score_breakdown.overall_score,
                experience_level=experience_level.value,
                recommendations_count=len(recommendations)
            )

            # Step 6: Enhance with LLM analysis
            logger.logger.info("starting_llm_analysis", filename=filename)

            llm_insights = await self._enhance_with_llm_analysis(
                content=cv_content,
                target_position=target_position,
                target_industry=target_industry
            )

            logger.logger.info(
                "llm_analysis_completed",
                filename=filename,
                insights_available=bool(llm_insights)
            )

            # Step 7: Build final result
            analysis_result = CVAnalysisResult(
                cv_content=cv_content,
                score_breakdown=score_breakdown,
                recommendations=recommendations,
                experience_level=experience_level,
                analysis_metadata={
                    "filename": filename,
                    "file_size": len(file_content),
                    "processing_time": datetime.utcnow().isoformat(),
                    "language_detected": detected_language,
                    "text_quality": quality_score,
                    "target_position": target_position,
                    "target_industry": target_industry,
                    "nlp_version": "1.0.0",
                    "ml_version": "1.0.0",
                    "llm_insights": llm_insights,
                }
            )
            
            logger.log_ml_prediction(
                model_name="cv_analyzer",
                confidence=score_breakdown.overall_score / 100,
                categories_predicted=len(enhanced_skills),
            )
            
            return analysis_result
            
        except FileProcessingError:
            raise
        except TextExtractionError:
            raise
        except NLPProcessingError:
            raise
        except Exception as e:
            logger.log_error(
                error_type="cv_analysis_failed",
                error_message=str(e),
                context={"filename": filename}
            )
            raise FileProcessingError(f"CV analysis failed: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        try:
            health_status = {
                "status": "healthy",
                "initialized": self.initialized,
                "components": {
                    "text_extractor": "ok",
                    "nlp_processor": "ok",
                    "adaptive_classifier": "ok",
                    "cv_scorer": "ok",
                }
            }
            
            # Check NLP processor health
            if self.initialized:
                nlp_health = await self.nlp_processor.health_check()
                health_status["components"]["nlp_processor"] = "healthy" if nlp_health else "unhealthy"
            
            return health_status
            
        except Exception as e:
            logger.log_error(
                error_type="health_check_failed",
                error_message=str(e),
                context={}
            )
            return {
                "status": "unhealthy",
                "error": str(e),
                "initialized": self.initialized,
            }
    
    async def get_supported_features(self) -> Dict[str, Any]:
        """Get supported features and capabilities"""
        return {
            "supported_languages": ["en", "he"],
            "supported_file_types": [".pdf", ".doc", ".docx"],
            "max_file_size_mb": settings.MAX_FILE_SIZE / (1024 * 1024),
            "features": {
                "text_extraction": True,
                "multilingual_nlp": True,
                "adaptive_learning": True,
                "skill_classification": True,
                "cv_scoring": True,
                "recommendations": True,
                "experience_level_detection": True,
                "pattern_recognition": True,
            },
            "scoring_categories": [
                "completeness",
                "relevance", 
                "experience",
                "skills",
                "education",
                "presentation",
            ],
            "skill_categories": [
                "technical",
                "soft",
                "language",
                "tool",
                "certification",
                "other",
            ],
        }

    async def _enhance_with_llm_analysis(
        self,
        content: CVContent,
        target_position: Optional[str] = None,
        target_industry: Optional[str] = None
    ) -> Dict[str, Any]:
        """Enhance CV analysis with LLM insights"""
        if not settings.ENABLE_LLM_ANALYSIS:
            return {}

        try:
            llm_insights = {}

            # Skills extraction and enhancement
            skills_request = LLMAnalysisRequest(
                text=content.raw_text,
                analysis_type="skills_extraction",
                language=content.detected_language.value
            )
            skills_response = await ollama_client.analyze_cv_content(skills_request)
            llm_insights["enhanced_skills"] = skills_response.insights

            # Experience analysis
            experience_request = LLMAnalysisRequest(
                text=content.raw_text,
                analysis_type="experience_analysis",
                language=content.detected_language.value
            )
            experience_response = await ollama_client.analyze_cv_content(experience_request)
            llm_insights["experience_insights"] = experience_response.insights

            # Content quality assessment
            quality_request = LLMAnalysisRequest(
                text=content.raw_text,
                analysis_type="content_quality",
                language=content.detected_language.value
            )
            quality_response = await ollama_client.analyze_cv_content(quality_request)
            llm_insights["quality_assessment"] = quality_response.insights

            # Job matching if target position provided
            if target_position or target_industry:
                job_context = {
                    "job_description": f"Position: {target_position or 'Not specified'}\nIndustry: {target_industry or 'Not specified'}"
                }
                matching_request = LLMAnalysisRequest(
                    text=content.raw_text,
                    analysis_type="job_matching",
                    context=job_context,
                    language=content.detected_language.value
                )
                matching_response = await ollama_client.analyze_cv_content(matching_request)
                llm_insights["job_matching"] = matching_response.insights

            logger.logger.info("llm_analysis_completed", insights_count=len(llm_insights))
            return llm_insights

        except Exception as e:
            logger.logger.error("llm_analysis_failed", error=str(e))
            return {"llm_error": str(e)}
