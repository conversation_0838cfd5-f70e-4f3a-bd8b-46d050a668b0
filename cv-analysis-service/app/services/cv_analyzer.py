"""
Main CV Analysis Service
"""

from typing import Optional, Dict, Any
from datetime import datetime
import time

from app.core.config import settings
from app.core.exceptions import FileProcessingError, TextExtractionError, NLPProcessingError
from app.core.logging import CVAnalysisLogger
from app.extractors.text_extractor import TextExtractor
from app.nlp.multilingual_processor import MultilingualNLPProcessor
from app.ml.adaptive_classifier import AdaptiveSkillClassifier
from app.ml.cv_scorer import CVScorer
from app.llm.ollama_client import ollama_client, LLMAnalysisRequest
from app.models.cv_analysis import (
    CVAnalysisResult, CVContent, PersonalInfo, Skill, Experience,
    Education, Language, Certification, ScoreBreakdown, Recommendation,
    ExperienceLevelEnum
)

logger = CVAnalysisLogger("cv_analyzer")


class CVAnalyzer:
    """Main CV analysis service orchestrator"""
    
    def __init__(self):
        self.text_extractor = TextExtractor()
        self.nlp_processor = MultilingualNLPProcessor()
        self.adaptive_classifier = AdaptiveSkillClassifier()
        self.cv_scorer = CVScorer()
        
        self.initialized = False
    
    async def initialize(self):
        """Initialize all components"""
        if self.initialized:
            return
        
        try:
            # Initialize NLP processor
            await self.nlp_processor.initialize()
            
            # Initialize adaptive classifier
            await self.adaptive_classifier.initialize()
            
            self.initialized = True
            logger.logger.info("cv_analyzer_initialized")
            
        except Exception as e:
            logger.log_error(
                error_type="cv_analyzer_init_failed",
                error_message=str(e),
                context={}
            )
            raise
    
    async def analyze_cv(
        self,
        file_content: bytes,
        filename: str,
        target_position: Optional[str] = None,
        target_industry: Optional[str] = None,
        language: str = "auto",
    ) -> CVAnalysisResult:
        """
        Comprehensive CV analysis

        Args:
            file_content: CV file content as bytes
            filename: Original filename
            target_position: Target job position
            target_industry: Target industry
            language: CV language (auto, en, he)

        Returns:
            Complete CV analysis results
        """
        if not self.initialized:
            await self.initialize()

        # Record start time for processing metrics
        start_time = time.time()

        try:
            # Step 1: Extract text from file
            logger.logger.info("starting_text_extraction", filename=filename)

            # Save file temporarily for processing
            import tempfile
            import os

            logger.logger.info("creating_temp_file", filename=filename)
            try:
                with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
                    temp_file.write(file_content)
                    temp_file_path = temp_file.name

                logger.logger.info("temp_file_created", filename=filename, temp_path=temp_file_path)
            except Exception as temp_error:
                logger.logger.error("temp_file_creation_failed", filename=filename, error=str(temp_error))
                raise

            try:
                file_info = {"filename": filename, "size": len(file_content)}
                logger.logger.info("calling_extract_text", filename=filename, file_info=file_info)
                try:
                    extraction_result = await self.text_extractor.extract_text(
                        temp_file_path, file_info
                    )
                    logger.logger.info("extract_text_returned", filename=filename, result_type=type(extraction_result).__name__)
                except Exception as extract_error:
                    import traceback
                    logger.logger.error("extract_text_failed", filename=filename, error=str(extract_error), traceback=traceback.format_exc())
                    raise
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    logger.logger.info("temp_file_cleaned", filename=filename)

            logger.logger.info(
                "text_extraction_raw_result",
                filename=filename,
                extraction_result_type=type(extraction_result).__name__,
                extraction_result_keys=list(extraction_result.keys()) if isinstance(extraction_result, dict) else "not_dict"
            )

            # Extract values from the result dictionary
            extracted_text = extraction_result.get("cleaned_text", "")
            detected_language = extraction_result.get("language", "en")
            quality_score = extraction_result.get("quality_score", 0.0)

            if not extracted_text.strip():
                raise TextExtractionError("No text could be extracted from the file")
            
            logger.logger.info(
                "text_extraction_completed",
                filename=filename,
                text_length=len(extracted_text),
                detected_language=detected_language,
                quality_score=quality_score
            )
            
            # Step 2: Process with NLP
            logger.logger.info("starting_nlp_processing", filename=filename)

            # Use detected language if auto mode
            processing_language = detected_language if language == "auto" else language

            try:
                nlp_result = await self.nlp_processor.process_cv_text(
                    text=extracted_text,
                    language=processing_language
                )
                logger.logger.info("nlp_processing_returned", filename=filename, result_type=type(nlp_result).__name__)
            except Exception as nlp_error:
                logger.logger.error("nlp_processing_failed", filename=filename, error=str(nlp_error))
                raise

            # Debug: Check the type and content of nlp_result
            logger.logger.info(
                "nlp_result_debug",
                filename=filename,
                nlp_result_type=type(nlp_result).__name__,
                nlp_result_content=str(nlp_result)[:200] if isinstance(nlp_result, str) else "dict"
            )

            # Ensure nlp_result is a dictionary
            if not isinstance(nlp_result, dict):
                raise NLPProcessingError(f"NLP processor returned {type(nlp_result).__name__} instead of dict: {str(nlp_result)[:100]}")

            logger.logger.info(
                "nlp_processing_completed",
                filename=filename,
                skills_found=len(nlp_result.get('skills', [])),
                experience_found=len(nlp_result.get('experience', [])),
                education_found=len(nlp_result.get('education', []))
            )
            
            # Step 3: Enhance skills with adaptive classification
            logger.logger.info("starting_skill_classification", filename=filename)
            
            enhanced_skills = []
            for skill_data in nlp_result.get('skills', []):
                # Handle both dict and Skill object formats
                if hasattr(skill_data, 'name'):
                    # skill_data is already a Skill object
                    skill_name = skill_data.name
                    skill_context = getattr(skill_data, 'context', '')
                    skill_years = getattr(skill_data, 'years_experience', None)
                else:
                    # skill_data is a dictionary
                    skill_name = skill_data.get('name', str(skill_data))
                    skill_context = skill_data.get('context', '')
                    skill_years = skill_data.get('years_experience')

                # Classify skill with adaptive classifier
                category, confidence = await self.adaptive_classifier.classify_skill(
                    skill_text=skill_name,
                    context=skill_context
                )

                enhanced_skill = Skill(
                    name=skill_name,
                    category=category,
                    confidence=confidence,
                    context=skill_context,
                    years_experience=skill_years,
                )
                enhanced_skills.append(enhanced_skill)
            
            logger.logger.info(
                "skill_classification_completed",
                filename=filename,
                classified_skills=len(enhanced_skills)
            )
            
            # Step 4: Build CV content structure
            personal_info = nlp_result.get('personal_info')
            if isinstance(personal_info, PersonalInfo):
                # If it's already a PersonalInfo object, use it directly
                cv_personal_info = personal_info
            else:
                # If it's a dict, create PersonalInfo from it
                personal_info_dict = personal_info if isinstance(personal_info, dict) else {}
                cv_personal_info = PersonalInfo(
                    full_name=personal_info_dict.get('full_name'),
                    email=personal_info_dict.get('email'),
                    phone=personal_info_dict.get('phone'),
                    location=personal_info_dict.get('location'),
                    linkedin=personal_info_dict.get('linkedin'),
                    summary=personal_info_dict.get('summary'),
                )

            cv_content = CVContent(
                personal_info=cv_personal_info,
                skills=enhanced_skills,
                experience=[
                    Experience(
                        position=exp.get('position') if hasattr(exp, 'get') else getattr(exp, 'position', ''),
                        company=exp.get('company') if hasattr(exp, 'get') else getattr(exp, 'company', ''),
                        start_date=exp.get('start_date') if hasattr(exp, 'get') else getattr(exp, 'start_date', ''),
                        end_date=exp.get('end_date') if hasattr(exp, 'get') else getattr(exp, 'end_date', ''),
                        duration_months=exp.get('duration_months') if hasattr(exp, 'get') else getattr(exp, 'duration_months', 0),
                        description=exp.get('description') if hasattr(exp, 'get') else getattr(exp, 'description', ''),
                        location=exp.get('location') if hasattr(exp, 'get') else getattr(exp, 'location', ''),
                    )
                    for exp in nlp_result.get('experience', [])
                ],
                education=[
                    Education(
                        institution=edu.get('institution') if hasattr(edu, 'get') else getattr(edu, 'institution', ''),
                        degree=edu.get('degree') if hasattr(edu, 'get') else getattr(edu, 'degree', ''),
                        field_of_study=edu.get('field_of_study') if hasattr(edu, 'get') else getattr(edu, 'field_of_study', ''),
                        start_date=edu.get('start_date') if hasattr(edu, 'get') else getattr(edu, 'start_date', ''),
                        end_date=edu.get('end_date') if hasattr(edu, 'get') else getattr(edu, 'end_date', ''),
                        gpa=edu.get('gpa') if hasattr(edu, 'get') else getattr(edu, 'gpa', None),
                        location=edu.get('location') if hasattr(edu, 'get') else getattr(edu, 'location', ''),
                    )
                    for edu in nlp_result.get('education', [])
                ],
                languages=[
                    Language(
                        name=lang.get('name'),
                        proficiency=lang.get('proficiency'),
                        native=lang.get('native', False),
                    )
                    for lang in nlp_result.get('languages', [])
                ],
                certifications=[
                    Certification(
                        name=cert.get('name'),
                        issuer=cert.get('issuer'),
                        date_obtained=cert.get('date_obtained'),
                        expiry_date=cert.get('expiry_date'),
                        credential_id=cert.get('credential_id'),
                    )
                    for cert in nlp_result.get('certifications', [])
                ],
                raw_text=extracted_text,
                detected_language=detected_language,
                text_quality_score=quality_score,
            )
            
            # Step 5: Score CV and generate recommendations
            logger.logger.info("starting_cv_scoring", filename=filename)
            
            score_breakdown, recommendations, experience_level = await self.cv_scorer.score_cv(
                cv_content=cv_content,
                target_position=target_position,
                target_industry=target_industry,
            )
            
            logger.logger.info(
                "cv_scoring_completed",
                filename=filename,
                overall_score=score_breakdown.overall_score,
                experience_level=experience_level.value,
                recommendations_count=len(recommendations)
            )

            # Step 6: Enhance with LLM analysis
            logger.logger.info("starting_llm_analysis", filename=filename)

            llm_insights = await self._enhance_with_llm_analysis(
                content=cv_content,
                target_position=target_position,
                target_industry=target_industry
            )

            logger.logger.info(
                "llm_analysis_completed",
                filename=filename,
                insights_available=bool(llm_insights)
            )

            # Step 7: Build final result
            from app.models.cv_analysis import CVFileInfo

            file_info = CVFileInfo(
                filename=filename,
                size=len(file_content),
                content_type="application/pdf"  # Default, could be enhanced
            )

            analysis_result = CVAnalysisResult(
                file_info=file_info,
                content=cv_content,
                score_breakdown=score_breakdown,
                recommendations=recommendations,
                experience_level=experience_level,
                processing_time=time.time() - start_time
            )
            
            logger.log_ml_prediction(
                model_name="cv_analyzer",
                confidence=score_breakdown.overall_score / 100,
                categories_predicted=len(enhanced_skills),
            )
            
            return analysis_result
            
        except FileProcessingError:
            raise
        except TextExtractionError:
            raise
        except NLPProcessingError:
            raise
        except Exception as e:
            import traceback
            full_traceback = traceback.format_exc()
            logger.log_error(
                error_type="cv_analysis_failed",
                error_message=str(e),
                context={"filename": filename, "traceback": full_traceback}
            )
            raise FileProcessingError(f"CV analysis failed: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        try:
            health_status = {
                "status": "healthy",
                "initialized": self.initialized,
                "components": {
                    "text_extractor": "ok",
                    "nlp_processor": "ok",
                    "adaptive_classifier": "ok",
                    "cv_scorer": "ok",
                }
            }
            
            # Check NLP processor health
            if self.initialized:
                nlp_health = await self.nlp_processor.health_check()
                health_status["components"]["nlp_processor"] = "healthy" if nlp_health else "unhealthy"
            
            return health_status
            
        except Exception as e:
            logger.log_error(
                error_type="health_check_failed",
                error_message=str(e),
                context={}
            )
            return {
                "status": "unhealthy",
                "error": str(e),
                "initialized": self.initialized,
            }
    
    async def get_supported_features(self) -> Dict[str, Any]:
        """Get supported features and capabilities"""
        return {
            "supported_languages": ["en", "he"],
            "supported_file_types": [".pdf", ".doc", ".docx"],
            "max_file_size_mb": settings.MAX_FILE_SIZE / (1024 * 1024),
            "features": {
                "text_extraction": True,
                "multilingual_nlp": True,
                "adaptive_learning": True,
                "skill_classification": True,
                "cv_scoring": True,
                "recommendations": True,
                "experience_level_detection": True,
                "pattern_recognition": True,
            },
            "scoring_categories": [
                "completeness",
                "relevance", 
                "experience",
                "skills",
                "education",
                "presentation",
            ],
            "skill_categories": [
                "technical",
                "soft",
                "language",
                "tool",
                "certification",
                "other",
            ],
        }

    async def _enhance_with_llm_analysis(
        self,
        content: CVContent,
        target_position: Optional[str] = None,
        target_industry: Optional[str] = None
    ) -> Dict[str, Any]:
        """Enhance CV analysis with LLM insights"""
        if not settings.ENABLE_LLM_ANALYSIS:
            return {}

        try:
            llm_insights = {}

            # Skills extraction and enhancement - מיומנויות
            skills_request = LLMAnalysisRequest(
                text=content.raw_text,
                analysis_type="skills_extraction",
                language=content.detected_language
            )
            skills_response = await ollama_client.analyze_cv_content(skills_request)
            llm_insights["enhanced_skills"] = skills_response.insights

            # Experience analysis - ניתוח ניסיון
            experience_request = LLMAnalysisRequest(
                text=content.raw_text,
                analysis_type="experience_analysis",
                language=content.detected_language
            )
            experience_response = await ollama_client.analyze_cv_content(experience_request)
            llm_insights["experience_insights"] = experience_response.insights

            # Content quality assessment - הערכת איכות
            quality_request = LLMAnalysisRequest(
                text=content.raw_text,
                analysis_type="content_quality",
                language=content.detected_language
            )
            quality_response = await ollama_client.analyze_cv_content(quality_request)
            llm_insights["quality_assessment"] = quality_response.insights

            # Overall assessment - הערכה כללית
            overall_request = LLMAnalysisRequest(
                text=content.raw_text,
                analysis_type="overall_assessment",
                language=content.detected_language
            )
            overall_response = await ollama_client.analyze_cv_content(overall_request)
            llm_insights["overall_assessment"] = overall_response.insights

            # Job matching if target position provided - התאמה למשרה
            if target_position or target_industry:
                job_context = {
                    "job_description": f"Position: {target_position or 'Not specified'}\nIndustry: {target_industry or 'Not specified'}"
                }
                matching_request = LLMAnalysisRequest(
                    text=content.raw_text,
                    analysis_type="job_matching",
                    context=job_context,
                    language=content.detected_language
                )
                matching_response = await ollama_client.analyze_cv_content(matching_request)
                llm_insights["job_matching"] = matching_response.insights

            # Add a summary section that combines key insights
            llm_insights["summary"] = self._create_insights_summary(llm_insights)

            logger.logger.info("llm_analysis_completed", insights_count=len(llm_insights))
            return llm_insights

        except Exception as e:
            logger.logger.error("llm_analysis_failed", error=str(e))
            return {"llm_error": str(e)}

    def _create_insights_summary(self, llm_insights: Dict[str, Any]) -> Dict[str, Any]:
        """Create a summary of key insights from all LLM analyses"""
        summary = {
            "key_strengths": [],
            "improvement_areas": [],
            "notable_skills": [],
            "career_insights": "",
            "overall_impression": ""
        }
        
        # Extract key strengths
        if "quality_assessment" in llm_insights:
            quality = llm_insights["quality_assessment"]
            # Find highest scoring areas
            if isinstance(quality, dict):
                scores = {
                    "Completeness": quality.get("completeness_score", 0),
                    "Clarity": quality.get("clarity_score", 0),
                    "Professional tone": quality.get("professional_tone", 0),
                    "Structure": quality.get("structure_quality", 0)
                }
                # Get top 2 strengths
                strengths = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:2]
                for area, score in strengths:
                    if score >= 70:  # Only include if score is good
                        summary["key_strengths"].append(f"Strong {area.lower()}: {score}/100")
        
        # Extract skills
        if "enhanced_skills" in llm_insights:
            skills = llm_insights["enhanced_skills"]
            if isinstance(skills, dict):
                # Get technical skills
                tech_skills = skills.get("technical_skills", [])
                if tech_skills and len(tech_skills) > 0:
                    summary["notable_skills"].extend(tech_skills[:3])  # Top 3 technical skills
                
                # Get soft skills
                soft_skills = skills.get("soft_skills", [])
                if soft_skills and len(soft_skills) > 0:
                    summary["notable_skills"].extend(soft_skills[:2])  # Top 2 soft skills
        
        # Extract improvement areas
        if "quality_assessment" in llm_insights:
            quality = llm_insights["quality_assessment"]
            if isinstance(quality, dict):
                suggestions = quality.get("improvement_suggestions", [])
                if suggestions and len(suggestions) > 0:
                    summary["improvement_areas"].extend(suggestions[:3])  # Top 3 suggestions
        
        # Extract career insights
        if "experience_insights" in llm_insights:
            experience = llm_insights["experience_insights"]
            if isinstance(experience, dict):
                level = experience.get("experience_level", "")
                progression = experience.get("career_progression", "")
                if level and progression:
                    summary["career_insights"] = f"{level} professional with {progression}"
        
        # Extract overall impression
        if "overall_assessment" in llm_insights:
            assessment = llm_insights["overall_assessment"]
            if isinstance(assessment, dict):
                overall = assessment.get("overall_assessment", "")
                if overall:
                    summary["overall_impression"] = overall
        
        return summary
