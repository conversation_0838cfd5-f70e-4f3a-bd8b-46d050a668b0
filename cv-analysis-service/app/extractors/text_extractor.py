"""
Text extraction from various CV file formats with Hebrew and English support
"""

import fitz  # PyMuPDF
import docx
import pdfplumber
import magic
import re
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import structlog
from langdetect import detect, LangDetectException

from app.core.exceptions import TextExtractionError, FileProcessingError
from app.core.logging import CVAnalysisLogger

logger = CVAnalysisLogger("text_extractor")


class TextExtractor:
    """Advanced text extractor with multilingual support"""
    
    def __init__(self):
        self.supported_formats = {
            'application/pdf': self._extract_from_pdf,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._extract_from_docx,
            'application/msword': self._extract_from_doc,
        }
        
        # Hebrew text patterns for better detection
        self.hebrew_pattern = re.compile(r'[\u0590-\u05FF]+')
        self.english_pattern = re.compile(r'[a-zA-Z]+')
    
    async def extract_text(self, file_path: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract text from CV file with language detection and quality assessment
        
        Args:
            file_path: Path to the file
            file_info: File metadata
            
        Returns:
            Dictionary containing extracted text and metadata
        """
        try:
            # Validate file
            self._validate_file(file_path, file_info)
            
            # Detect MIME type
            mime_type = magic.from_file(file_path, mime=True)
            
            if mime_type not in self.supported_formats:
                raise TextExtractionError(
                    f"Unsupported file format: {mime_type}",
                    details={"mime_type": mime_type, "filename": file_info.get("filename")}
                )
            
            # Extract text using appropriate method
            extractor_func = self.supported_formats[mime_type]
            raw_text = await extractor_func(file_path)
            
            if not raw_text or len(raw_text.strip()) < 50:
                raise TextExtractionError(
                    "Insufficient text extracted from file",
                    details={"text_length": len(raw_text), "filename": file_info.get("filename")}
                )
            
            # Clean and process text
            cleaned_text = self._clean_text(raw_text)
            
            # Detect language
            language_info = self._detect_language(cleaned_text)
            
            # Assess text quality
            quality_score = self._assess_text_quality(cleaned_text, language_info)
            
            # Extract structure information
            structure_info = self._analyze_structure(cleaned_text)
            
            result = {
                "raw_text": raw_text,
                "cleaned_text": cleaned_text,
                "language": language_info["primary_language"],
                "language_confidence": language_info["confidence"],
                "language_distribution": language_info["distribution"],
                "text_length": len(cleaned_text),
                "quality_score": quality_score,
                "structure": structure_info,
                "extraction_method": mime_type,
            }
            
            logger.log_text_extraction(
                filename=file_info.get("filename", "unknown"),
                extracted_length=len(cleaned_text),
                language_detected=language_info["primary_language"],
            )
            
            return result
            
        except Exception as e:
            logger.log_error(
                error_type="text_extraction_failed",
                error_message=str(e),
                context={"filename": file_info.get("filename"), "file_path": file_path}
            )
            raise TextExtractionError(f"Failed to extract text: {str(e)}")
    
    def _validate_file(self, file_path: str, file_info: Dict[str, Any]) -> None:
        """Validate file before processing"""
        if not Path(file_path).exists():
            raise FileProcessingError(f"File not found: {file_path}")
        
        file_size = Path(file_path).stat().st_size
        if file_size > 4 * 1024 * 1024:  # 4MB
            raise FileProcessingError(f"File too large: {file_size} bytes")
        
        if file_size == 0:
            raise FileProcessingError("Empty file")
    
    async def _extract_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF using multiple methods for best results"""
        text_parts = []
        
        try:
            # Method 1: PyMuPDF (best for most PDFs)
            doc = fitz.open(file_path)
            for page in doc:
                text_parts.append(page.get_text())
            doc.close()
            
            text = "\n".join(text_parts)
            
            # If text is too short, try pdfplumber (better for tables)
            if len(text.strip()) < 100:
                text_parts = []
                with pdfplumber.open(file_path) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text_parts.append(page_text)
                text = "\n".join(text_parts)
            
            return text
            
        except Exception as e:
            raise TextExtractionError(f"PDF extraction failed: {str(e)}")
    
    async def _extract_from_docx(self, file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(file_path)
            text_parts = []
            
            # Extract paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            # Extract tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_parts.append(" | ".join(row_text))
            
            return "\n".join(text_parts)
            
        except Exception as e:
            raise TextExtractionError(f"DOCX extraction failed: {str(e)}")
    
    async def _extract_from_doc(self, file_path: str) -> str:
        """Extract text from DOC file (legacy format)"""
        try:
            # For DOC files, we'll use python-docx which has limited support
            # In production, consider using antiword or other tools
            doc = docx.Document(file_path)
            text_parts = [p.text for p in doc.paragraphs if p.text.strip()]
            return "\n".join(text_parts)
            
        except Exception as e:
            raise TextExtractionError(f"DOC extraction failed: {str(e)}")
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep Hebrew and English
        text = re.sub(r'[^\u0590-\u05FF\u0020-\u007Ea-zA-Z0-9\s\-\.\,\:\;\(\)\[\]\/\@\+]', ' ', text)
        
        # Normalize line breaks
        text = re.sub(r'\n+', '\n', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        return text
    
    def _detect_language(self, text: str) -> Dict[str, Any]:
        """Detect language with Hebrew and English support"""
        if not text:
            return {"primary_language": "unknown", "confidence": 0.0, "distribution": {}}
        
        # Count Hebrew and English characters
        hebrew_chars = len(self.hebrew_pattern.findall(text))
        english_chars = len(self.english_pattern.findall(text))
        total_chars = hebrew_chars + english_chars
        
        if total_chars == 0:
            return {"primary_language": "unknown", "confidence": 0.0, "distribution": {}}
        
        hebrew_ratio = hebrew_chars / total_chars
        english_ratio = english_chars / total_chars
        
        # Use langdetect as backup
        detected_lang = "unknown"
        confidence = 0.0
        
        try:
            detected_lang = detect(text)
            confidence = 0.8  # langdetect doesn't provide confidence
        except LangDetectException:
            pass
        
        # Determine primary language
        if hebrew_ratio > 0.3:
            primary_language = "he"
            confidence = max(confidence, hebrew_ratio)
        elif english_ratio > 0.3:
            primary_language = "en"
            confidence = max(confidence, english_ratio)
        elif detected_lang in ["he", "en"]:
            primary_language = detected_lang
        else:
            primary_language = "en"  # Default to English
        
        return {
            "primary_language": primary_language,
            "confidence": confidence,
            "distribution": {
                "hebrew": hebrew_ratio,
                "english": english_ratio,
                "detected": detected_lang,
            }
        }
    
    def _assess_text_quality(self, text: str, language_info: Dict[str, Any]) -> float:
        """Assess the quality of extracted text"""
        if not text:
            return 0.0
        
        score = 0.0
        
        # Length score (0-25 points)
        length = len(text)
        if length > 2000:
            score += 25
        elif length > 1000:
            score += 20
        elif length > 500:
            score += 15
        elif length > 200:
            score += 10
        else:
            score += 5
        
        # Language detection confidence (0-25 points)
        score += language_info["confidence"] * 25
        
        # Structure indicators (0-25 points)
        structure_indicators = [
            r'(?i)(experience|ניסיון)',
            r'(?i)(education|השכלה)',
            r'(?i)(skills|כישורים)',
            r'(?i)(email|מייל)',
            r'(?i)(phone|טלפון)',
        ]
        
        found_indicators = sum(1 for pattern in structure_indicators if re.search(pattern, text))
        score += (found_indicators / len(structure_indicators)) * 25
        
        # Text coherence (0-25 points)
        sentences = re.split(r'[.!?]+', text)
        avg_sentence_length = sum(len(s.split()) for s in sentences) / max(len(sentences), 1)
        
        if 5 <= avg_sentence_length <= 20:
            score += 25
        elif 3 <= avg_sentence_length <= 25:
            score += 20
        else:
            score += 10
        
        return min(score, 100.0)
    
    def _analyze_structure(self, text: str) -> Dict[str, Any]:
        """Analyze document structure"""
        structure = {
            "has_contact_info": False,
            "has_experience": False,
            "has_education": False,
            "has_skills": False,
            "sections_detected": [],
            "bullet_points": 0,
            "line_count": 0,
        }
        
        lines = text.split('\n')
        structure["line_count"] = len(lines)
        
        # Count bullet points
        structure["bullet_points"] = len(re.findall(r'^\s*[•\-\*]\s+', text, re.MULTILINE))
        
        # Detect sections
        section_patterns = {
            "contact": r'(?i)(email|phone|address|linkedin|github|מייל|טלפון|כתובת)',
            "experience": r'(?i)(experience|work|employment|ניסיון|עבודה)',
            "education": r'(?i)(education|university|degree|השכלה|אוניברסיטה|תואר)',
            "skills": r'(?i)(skills|technologies|כישורים|טכנולוגיות)',
        }
        
        for section, pattern in section_patterns.items():
            if re.search(pattern, text):
                structure[f"has_{section}"] = True
                structure["sections_detected"].append(section)
        
        return structure
