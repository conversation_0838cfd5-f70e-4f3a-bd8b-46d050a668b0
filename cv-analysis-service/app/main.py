"""
CVmatic Analysis Service - Main Application
"""

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import time
import structlog

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.cv_analysis import router as cv_analysis_router
from app.core.exceptions import CVAnalysisException
from app.services.cv_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.ml.learning_manager import LearningManager
from app.monitoring.health import health_monitor

# Setup logging
setup_logging()
logger = structlog.get_logger()

# Create FastAPI app
app = FastAPI(
    title="CVmatic Analysis Service",
    description="Advanced CV analysis with multilingual support and adaptive learning",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# Request timing middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    # Log request
    logger.info(
        "request_processed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time,
    )
    
    return response

# Exception handlers
@app.exception_handler(CVAnalysisException)
async def cv_analysis_exception_handler(request: Request, exc: CVAnalysisException):
    logger.error(
        "cv_analysis_error",
        error=str(exc),
        error_code=exc.error_code,
        url=str(request.url),
    )
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.error_code,
            "message": str(exc),
            "details": exc.details,
        },
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(
        "unexpected_error",
        error=str(exc),
        error_type=type(exc).__name__,
        url=str(request.url),
    )
    return JSONResponse(
        status_code=500,
        content={
            "error": "internal_server_error",
            "message": "An unexpected error occurred",
        },
    )

# Include API routes
app.include_router(cv_analysis_router)

# Global service instances
cv_analyzer: CVAnalyzer = None
learning_manager: LearningManager = None

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    global cv_analyzer, learning_manager

    health_status = {
        "status": "healthy",
        "service": "cvmatic-analysis",
        "version": "1.0.0",
        "timestamp": time.time(),
        "components": {
            "cv_analyzer": "unknown",
            "learning_manager": "unknown",
        }
    }

    try:
        # Check CV analyzer health
        if cv_analyzer:
            analyzer_health = await cv_analyzer.health_check()
            health_status["components"]["cv_analyzer"] = analyzer_health.get("status", "unknown")

        # Check learning manager health
        if learning_manager:
            health_status["components"]["learning_manager"] = "healthy"

        # Overall status
        component_statuses = list(health_status["components"].values())
        if all(status in ["healthy", "ok"] for status in component_statuses):
            health_status["status"] = "healthy"
        elif any(status == "unhealthy" for status in component_statuses):
            health_status["status"] = "unhealthy"
        else:
            health_status["status"] = "degraded"

    except Exception as e:
        logger.error("health_check_failed", error=str(e))
        health_status["status"] = "unhealthy"
        health_status["error"] = str(e)

    return health_status

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "CVmatic Analysis Service",
        "version": "1.0.0",
        "description": "Advanced CV analysis with multilingual support",
        "docs": "/docs" if settings.ENVIRONMENT == "development" else None,
    }

# Startup event
@app.on_event("startup")
async def startup_event():
    global cv_analyzer, learning_manager

    logger.info("cvmatic_analysis_service_starting")

    try:
        # Initialize CV analyzer
        cv_analyzer = CVAnalyzer()
        await cv_analyzer.initialize()
        logger.info("cv_analyzer_initialized")

        # Initialize learning manager
        learning_manager = LearningManager()
        await learning_manager.initialize()
        logger.info("learning_manager_initialized")

        # Register health checks
        health_monitor.register_health_check("cv_analyzer", cv_analyzer.health_check)
        health_monitor.register_health_check("learning_manager", lambda: True)  # Simple check

        # Start health monitoring
        await health_monitor.start_monitoring()
        logger.info("health_monitoring_started")

        logger.info("cvmatic_analysis_service_started")

    except Exception as e:
        logger.error("initialization_failed", error=str(e))
        raise

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("cvmatic_analysis_service_shutting_down")

    # Stop health monitoring
    await health_monitor.stop_monitoring()
    logger.info("health_monitoring_stopped")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.ENVIRONMENT == "development",
    )
