"""
Configuration settings for CVmatic Analysis Service
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "CVmatic Analysis Service"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = False
    
    # API
    API_V1_STR: str = "/api/v1"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:3001"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1", "*"]
    
    # Database
    DATABASE_URL: str = "postgresql://cvmatic:cvmatic123@localhost:5432/cvmatic_analysis"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_EXPIRE_TIME: int = 3600  # 1 hour
    
    # File Processing
    MAX_FILE_SIZE: int = 4 * 1024 * 1024  # 4MB
    ALLOWED_FILE_TYPES: List[str] = ["pdf", "doc", "docx"]
    UPLOAD_DIR: str = "/tmp/cvmatic_uploads"
    
    # NLP Models
    SPACY_MODEL_EN: str = "en_core_web_lg"
    SPACY_MODEL_HE: str = "he_core_news_sm"
    TRANSFORMERS_CACHE_DIR: str = "/app/data/models/transformers"
    
    # Machine Learning
    ML_MODELS_DIR: str = "/app/data/models"
    CATEGORIES_DB_PATH: str = "/app/data/categories"
    SKILLS_DB_PATH: str = "/app/data/skills"
    MIN_CONFIDENCE_THRESHOLD: float = 0.7
    LEARNING_RATE: float = 0.001
    
    # Analysis Settings
    DEFAULT_LANGUAGE: str = "auto"  # auto, en, he
    ENABLE_ADAPTIVE_LEARNING: bool = True
    FEEDBACK_LEARNING_THRESHOLD: int = 10  # Minimum feedback samples to trigger learning
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 3600  # 1 hour
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    LOG_FILE: Optional[str] = "/app/logs/cvmatic.log"
    
    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 8001
    
    # Hebrew Language Support
    HEBREW_TOKENIZER_MODEL: str = "alephbert-base"
    HEBREW_SKILLS_DB: str = "/app/data/skills/hebrew_skills.json"
    HEBREW_CATEGORIES_DB: str = "/app/data/categories/hebrew_categories.json"
    
    # LLM Integration
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "llama3:8b"
    OLLAMA_TIMEOUT: int = 120  # seconds
    ENABLE_LLM_ANALYSIS: bool = True

    # External APIs
    OPENAI_API_KEY: Optional[str] = None
    HUGGINGFACE_API_KEY: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get application settings"""
    return settings

# Ensure directories exist
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.ML_MODELS_DIR, exist_ok=True)
os.makedirs(settings.CATEGORIES_DB_PATH, exist_ok=True)
os.makedirs(settings.SKILLS_DB_PATH, exist_ok=True)
os.makedirs(settings.TRANSFORMERS_CACHE_DIR, exist_ok=True)

if settings.LOG_FILE:
    os.makedirs(os.path.dirname(settings.LOG_FILE), exist_ok=True)
