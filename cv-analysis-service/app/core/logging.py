"""
Logging configuration for CVmatic Analysis Service
"""

import structlog
import logging
import sys
from typing import Any, Dict
from app.core.config import settings


def setup_logging() -> None:
    """Setup structured logging"""
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )
    
    # Configure structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
    ]
    
    if settings.LOG_FORMAT == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.Console<PERSON>())
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )


class CVAnalysisLogger:
    """Custom logger for CV analysis operations"""
    
    def __init__(self, name: str):
        self.logger = structlog.get_logger(name)
    
    def log_analysis_start(self, file_info: Dict[str, Any]) -> None:
        """Log analysis start"""
        self.logger.info(
            "analysis_started",
            filename=file_info.get("filename"),
            file_size=file_info.get("size"),
            file_type=file_info.get("type"),
        )
    
    def log_analysis_complete(
        self,
        analysis_id: str,
        duration: float,
        score: float,
        language: str,
    ) -> None:
        """Log analysis completion"""
        self.logger.info(
            "analysis_completed",
            analysis_id=analysis_id,
            duration=duration,
            score=score,
            language=language,
        )
    
    def log_text_extraction(
        self,
        filename: str,
        extracted_length: int,
        language_detected: str,
    ) -> None:
        """Log text extraction"""
        self.logger.info(
            "text_extracted",
            filename=filename,
            text_length=extracted_length,
            language=language_detected,
        )
    
    def log_nlp_processing(
        self,
        language: str,
        entities_found: int,
        skills_extracted: int,
    ) -> None:
        """Log NLP processing"""
        self.logger.info(
            "nlp_processed",
            language=language,
            entities_count=entities_found,
            skills_count=skills_extracted,
        )
    
    def log_ml_prediction(
        self,
        model_name: str,
        confidence: float,
        categories_predicted: int,
    ) -> None:
        """Log ML prediction"""
        self.logger.info(
            "ml_prediction",
            model=model_name,
            confidence=confidence,
            categories=categories_predicted,
        )
    
    def log_learning_event(
        self,
        event_type: str,
        feedback_count: int,
        model_updated: bool,
    ) -> None:
        """Log learning events"""
        self.logger.info(
            "learning_event",
            type=event_type,
            feedback_count=feedback_count,
            model_updated=model_updated,
        )
    
    def log_error(
        self,
        error_type: str,
        error_message: str,
        context: Dict[str, Any],
    ) -> None:
        """Log errors with context"""
        self.logger.error(
            "error_occurred",
            error_type=error_type,
            error_message=error_message,
            **context,
        )
