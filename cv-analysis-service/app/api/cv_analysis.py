"""
CV Analysis API endpoints
"""

import asyncio
from typing import Optional, Dict, Any
from fastapi import APIRouter, UploadFile, File, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse, PlainTextResponse
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
import structlog

from app.core.config import settings
from app.core.exceptions import FileProcessingError, TextExtractionError, NLPProcessingError
from app.core.logging import CVAnalysisLogger
from app.models.cv_analysis import (
    CVAnalysisResult, CVAnalysisRequest, FeedbackData,
    AnalysisStatus, LearningInsights
)
from app.services.cv_analyzer import CVAnalyzer
from app.ml.learning_manager import LearningManager
from app.monitoring.health import health_monitor
from app.monitoring.metrics import (
    record_file_upload, record_ml_prediction, record_learning_feedback,
    track_request_metrics, track_cv_analysis_metrics
)

logger = CVAnalysisLogger("cv_analysis_api")
router = APIRouter(prefix="/api/v1/cv-analysis", tags=["CV Analysis"])

# Global instances
cv_analyzer: Optional[CVAnalyzer] = None
learning_manager: Optional[LearningManager] = None


async def get_cv_analyzer() -> CVAnalyzer:
    """Get CV analyzer instance"""
    global cv_analyzer
    if cv_analyzer is None:
        cv_analyzer = CVAnalyzer()
        await cv_analyzer.initialize()
    return cv_analyzer


async def get_learning_manager() -> LearningManager:
    """Get learning manager instance"""
    global learning_manager
    if learning_manager is None:
        learning_manager = LearningManager()
        await learning_manager.initialize()
    return learning_manager


@router.post("/analyze", response_model=CVAnalysisResult)
async def analyze_cv(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    target_position: Optional[str] = Query(None, description="Target job position"),
    target_industry: Optional[str] = Query(None, description="Target industry"),
    language: Optional[str] = Query("auto", description="CV language (auto, en, he)"),
) -> CVAnalysisResult:
    """
    Analyze uploaded CV file
    
    Args:
        file: CV file (PDF, DOC, DOCX)
        target_position: Target job position for relevance scoring
        target_industry: Target industry for relevance scoring
        language: CV language for processing optimization
        
    Returns:
        Comprehensive CV analysis results
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Check file size
        if file.size and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE / (1024*1024):.1f}MB"
            )
        
        # Check file type
        allowed_types = [".pdf", ".doc", ".docx"]
        file_extension = "." + file.filename.split(".")[-1].lower()
        if file_extension not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type. Allowed: {', '.join(allowed_types)}"
            )
        
        # Get analyzer
        analyzer = await get_cv_analyzer()
        print(f"DEBUG API: Got analyzer: {type(analyzer)}")

        # Read file content
        file_content = await file.read()
        print(f"DEBUG API: Read file content, length: {len(file_content)}")

        # Analyze CV
        print(f"DEBUG API: About to call analyzer.analyze_cv")
        analysis_result = await analyzer.analyze_cv(
            file_content=file_content,
            filename=file.filename,
            target_position=target_position,
            target_industry=target_industry,
            language=language,
        )
        
        # Process for learning in background
        learning_mgr = await get_learning_manager()
        background_tasks.add_task(
            learning_mgr.process_cv_analysis,
            analysis_result.cv_content,
            analysis_result.dict()
        )
        
        logger.log_api_request(
            endpoint="analyze_cv",
            file_type=file_extension,
            file_size=file.size or 0,
            target_position=target_position,
            target_industry=target_industry,
        )
        
        return analysis_result
        
    except FileProcessingError as e:
        logger.log_error(
            error_type="file_processing_error",
            error_message=str(e),
            context={"filename": file.filename}
        )
        raise HTTPException(status_code=422, detail=f"File processing failed: {str(e)}")
        
    except TextExtractionError as e:
        logger.log_error(
            error_type="text_extraction_error",
            error_message=str(e),
            context={"filename": file.filename}
        )
        raise HTTPException(status_code=422, detail=f"Text extraction failed: {str(e)}")
        
    except NLPProcessingError as e:
        logger.log_error(
            error_type="nlp_processing_error",
            error_message=str(e),
            context={"filename": file.filename}
        )
        raise HTTPException(status_code=500, detail=f"Analysis processing failed: {str(e)}")
        
    except Exception as e:
        logger.log_error(
            error_type="unexpected_error",
            error_message=str(e),
            context={"filename": file.filename}
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/analyze-batch")
async def analyze_cv_batch(
    background_tasks: BackgroundTasks,
    files: list[UploadFile] = File(...),
    target_position: Optional[str] = Query(None),
    target_industry: Optional[str] = Query(None),
) -> Dict[str, Any]:
    """
    Analyze multiple CV files in batch
    
    Args:
        files: List of CV files
        target_position: Target job position
        target_industry: Target industry
        
    Returns:
        Batch analysis status and results
    """
    try:
        if len(files) > settings.MAX_BATCH_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"Too many files. Maximum batch size: {settings.MAX_BATCH_SIZE}"
            )
        
        analyzer = await get_cv_analyzer()
        learning_mgr = await get_learning_manager()
        
        # Process files concurrently
        tasks = []
        for file in files:
            file_content = await file.read()
            task = analyzer.analyze_cv(
                file_content=file_content,
                filename=file.filename,
                target_position=target_position,
                target_industry=target_industry,
            )
            tasks.append(task)
        
        # Execute batch analysis
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        successful_results = []
        failed_results = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_results.append({
                    "filename": files[i].filename,
                    "error": str(result),
                })
            else:
                successful_results.append({
                    "filename": files[i].filename,
                    "analysis": result,
                })
                
                # Add to learning background tasks
                background_tasks.add_task(
                    learning_mgr.process_cv_analysis,
                    result.cv_content,
                    result.dict()
                )
        
        logger.log_api_request(
            endpoint="analyze_cv_batch",
            batch_size=len(files),
            successful_count=len(successful_results),
            failed_count=len(failed_results),
        )
        
        return {
            "total_files": len(files),
            "successful_analyses": len(successful_results),
            "failed_analyses": len(failed_results),
            "results": successful_results,
            "errors": failed_results,
        }
        
    except Exception as e:
        logger.log_error(
            error_type="batch_analysis_error",
            error_message=str(e),
            context={"batch_size": len(files)}
        )
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")


@router.post("/feedback")
async def submit_feedback(feedback_data: FeedbackData) -> Dict[str, Any]:
    """
    Submit feedback for learning improvement
    
    Args:
        feedback_data: User feedback data
        
    Returns:
        Feedback processing status
    """
    try:
        learning_mgr = await get_learning_manager()
        result = await learning_mgr.receive_feedback(feedback_data)
        
        logger.log_api_request(
            endpoint="submit_feedback",
            feedback_type=feedback_data.feedback_type,
            cv_id=feedback_data.cv_id,
        )
        
        return result
        
    except Exception as e:
        logger.log_error(
            error_type="feedback_submission_error",
            error_message=str(e),
            context={"feedback_type": feedback_data.feedback_type}
        )
        raise HTTPException(status_code=500, detail=f"Feedback submission failed: {str(e)}")


@router.get("/learning-insights", response_model=LearningInsights)
async def get_learning_insights() -> LearningInsights:
    """
    Get learning system insights and metrics
    
    Returns:
        Learning insights and system metrics
    """
    try:
        learning_mgr = await get_learning_manager()
        insights = await learning_mgr.get_learning_insights()
        
        logger.log_api_request(
            endpoint="get_learning_insights",
        )
        
        return LearningInsights(**insights)
        
    except Exception as e:
        logger.log_error(
            error_type="learning_insights_error",
            error_message=str(e),
            context={}
        )
        raise HTTPException(status_code=500, detail=f"Failed to get learning insights: {str(e)}")


@router.post("/llm-analysis")
async def analyze_with_llm(
    file: UploadFile = File(...),
    analysis_type: str = Query("skills_extraction", description="Type of LLM analysis"),
    target_position: Optional[str] = Query(None, description="Target job position"),
    target_industry: Optional[str] = Query(None, description="Target industry"),
    language: str = Query("auto", description="CV language (auto, en, he)")
) -> Dict[str, Any]:
    """
    Perform LLM-based analysis on CV content

    Args:
        file: CV file to analyze
        analysis_type: Type of analysis (skills_extraction, experience_analysis, content_quality, job_matching)
        target_position: Target job position for job matching
        target_industry: Target industry for job matching
        language: CV language

    Returns:
        LLM analysis results
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")

        # Check file size
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE / (1024*1024):.1f}MB"
            )

        # Get analyzer and extract text
        analyzer = await get_cv_analyzer()

        # Extract text from file
        import tempfile
        import os

        # Save file temporarily for processing
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            file_info = {"filename": file.filename, "size": len(file_content)}
            extraction_result = await analyzer.text_extractor.extract_text(
                temp_file_path, file_info
            )

            # Extract values from the result dictionary
            extracted_text = extraction_result.get("cleaned_text", "")
            detected_language = extraction_result.get("language", "en")
            quality_score = extraction_result.get("quality_score", 0.0)
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

        if not extracted_text.strip():
            raise HTTPException(status_code=400, detail="No text could be extracted from the file")

        # Import LLM client
        from app.llm.ollama_client import ollama_client, LLMAnalysisRequest

        # Prepare context for job matching
        context = None
        if analysis_type == "job_matching" and (target_position or target_industry):
            context = {
                "job_description": f"Position: {target_position or 'Not specified'}\nIndustry: {target_industry or 'Not specified'}"
            }

        # Create LLM request
        llm_request = LLMAnalysisRequest(
            text=extracted_text,
            analysis_type=analysis_type,
            context=context,
            language=detected_language if language == "auto" else language
        )

        # Perform LLM analysis
        llm_response = await ollama_client.analyze_cv_content(llm_request)

        # Record metrics
        record_ml_prediction(
            model_name="ollama_llm",
            confidence=llm_response.confidence,
            prediction_type=analysis_type
        )

        logger.logger.info(
            "llm_analysis_completed",
            filename=file.filename,
            analysis_type=analysis_type,
            confidence=llm_response.confidence,
            processing_time=llm_response.processing_time
        )

        return {
            "analysis_type": llm_response.analysis_type,
            "insights": llm_response.insights,
            "confidence": llm_response.confidence,
            "processing_time": llm_response.processing_time,
            "model_used": llm_response.model_used,
            "file_info": {
                "filename": file.filename,
                "detected_language": detected_language,
                "text_quality": quality_score,
                "text_length": len(extracted_text)
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.log_error(
            error_type="llm_analysis_error",
            error_message=str(e),
            context={"filename": file.filename, "analysis_type": analysis_type}
        )
        raise HTTPException(status_code=500, detail=f"LLM analysis failed: {str(e)}")


@router.post("/force-model-update")
async def force_model_update() -> Dict[str, Any]:
    """
    Force immediate model update with available feedback
    
    Returns:
        Model update status
    """
    try:
        learning_mgr = await get_learning_manager()
        result = await learning_mgr.force_model_update()
        
        logger.log_api_request(
            endpoint="force_model_update",
        )
        
        return result
        
    except Exception as e:
        logger.log_error(
            error_type="model_update_error",
            error_message=str(e),
            context={}
        )
        raise HTTPException(status_code=500, detail=f"Model update failed: {str(e)}")


@router.get("/status")
async def get_analysis_status() -> AnalysisStatus:
    """
    Get CV analysis service status
    
    Returns:
        Service status and health information
    """
    try:
        analyzer = await get_cv_analyzer()
        learning_mgr = await get_learning_manager()
        
        # Check component health
        nlp_status = await analyzer.nlp_processor.health_check()
        ml_status = await analyzer.ml_manager.health_check() if hasattr(analyzer, 'ml_manager') else {"status": "ok"}
        
        status = AnalysisStatus(
            service_status="healthy",
            nlp_models_loaded=nlp_status.get("models_loaded", True),
            ml_models_loaded=ml_status.get("models_loaded", True),
            learning_system_active=True,
            supported_languages=["en", "he"],
            supported_file_types=[".pdf", ".doc", ".docx"],
            max_file_size_mb=settings.MAX_FILE_SIZE / (1024 * 1024),
            version="1.0.0",
        )
        
        return status
        
    except Exception as e:
        logger.log_error(
            error_type="status_check_error",
            error_message=str(e),
            context={}
        )
        return AnalysisStatus(
            service_status="unhealthy",
            nlp_models_loaded=False,
            ml_models_loaded=False,
            learning_system_active=False,
            supported_languages=[],
            supported_file_types=[],
            max_file_size_mb=0,
            version="1.0.0",
        )


@router.get("/metrics")
async def get_metrics():
    """
    Prometheus metrics endpoint

    Returns:
        Prometheus metrics in text format
    """
    try:
        metrics_data = generate_latest()
        return PlainTextResponse(
            content=metrics_data.decode('utf-8'),
            media_type=CONTENT_TYPE_LATEST
        )
    except Exception as e:
        logger.log_error(
            error_type="metrics_export_error",
            error_message=str(e),
            context={}
        )
        raise HTTPException(status_code=500, detail="Failed to export metrics")


@router.get("/health/detailed")
async def get_detailed_health() -> Dict[str, Any]:
    """
    Get detailed system health information

    Returns:
        Comprehensive health status and diagnostics
    """
    try:
        health_status = await health_monitor.get_system_health()

        logger.log_api_request(
            endpoint="get_detailed_health",
        )

        return health_status

    except Exception as e:
        logger.log_error(
            error_type="detailed_health_error",
            error_message=str(e),
            context={}
        )
        raise HTTPException(status_code=500, detail="Failed to get health status")


@router.get("/diagnostics")
async def run_system_diagnostics() -> Dict[str, Any]:
    """
    Run comprehensive system diagnostics

    Returns:
        System diagnostics and performance metrics
    """
    try:
        diagnostics = await health_monitor.run_diagnostics()

        logger.log_api_request(
            endpoint="run_system_diagnostics",
        )

        return diagnostics

    except Exception as e:
        logger.log_error(
            error_type="diagnostics_error",
            error_message=str(e),
            context={}
        )
        raise HTTPException(status_code=500, detail="Failed to run diagnostics")


@router.get("/performance")
async def get_performance_metrics() -> Dict[str, Any]:
    """
    Get system performance metrics

    Returns:
        Performance metrics and statistics
    """
    try:
        performance = await health_monitor.get_performance_metrics()

        logger.log_api_request(
            endpoint="get_performance_metrics",
        )

        return performance

    except Exception as e:
        logger.log_error(
            error_type="performance_metrics_error",
            error_message=str(e),
            context={}
        )
        raise HTTPException(status_code=500, detail="Failed to get performance metrics")


@router.get("/llm-health")
async def check_llm_health() -> Dict[str, Any]:
    """
    Check LLM service health and model availability

    Returns:
        LLM service status and model information
    """
    try:
        from app.llm.ollama_client import ollama_client

        # Check basic connectivity
        is_healthy = await ollama_client.health_check()

        # Check model availability
        model_available = False
        if is_healthy:
            model_available = await ollama_client.ensure_model_available()

        status = {
            "service_healthy": is_healthy,
            "model_available": model_available,
            "model_name": ollama_client.model_name,
            "base_url": ollama_client.base_url,
            "llm_enabled": settings.ENABLE_LLM_ANALYSIS
        }

        logger.logger.info("llm_health_check", status=status)

        return status

    except Exception as e:
        logger.log_error(
            error_type="llm_health_check_error",
            error_message=str(e),
            context={}
        )
        return {
            "service_healthy": False,
            "model_available": False,
            "error": str(e),
            "llm_enabled": settings.ENABLE_LLM_ANALYSIS
        }
