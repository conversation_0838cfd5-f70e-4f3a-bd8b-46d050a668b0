"""
Prometheus metrics for CV analysis service
"""

from prometheus_client import Counter, Histogram, Gauge, Info
import time
from typing import Dict, Any
from functools import wraps

# Service info
service_info = Info('cvmatic_service_info', 'CVmatic Analysis Service Information')
service_info.info({
    'version': '1.0.0',
    'service': 'cv-analysis',
    'description': 'Advanced CV analysis with multilingual support and adaptive learning'
})

# Request metrics
http_requests_total = Counter(
    'cvmatic_http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

http_request_duration_seconds = Histogram(
    'cvmatic_http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

# CV analysis metrics
cv_analyses_total = Counter(
    'cvmatic_cv_analyses_total',
    'Total CV analyses performed',
    ['language', 'file_type', 'status']
)

cv_analysis_duration_seconds = Histogram(
    'cvmatic_cv_analysis_duration_seconds',
    'CV analysis duration in seconds',
    ['language', 'file_type']
)

cv_analysis_score_distribution = Histogram(
    'cvmatic_cv_analysis_score_distribution',
    'Distribution of CV analysis scores',
    buckets=[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
)

# Text extraction metrics
text_extraction_total = Counter(
    'cvmatic_text_extraction_total',
    'Total text extractions',
    ['file_type', 'status']
)

text_extraction_duration_seconds = Histogram(
    'cvmatic_text_extraction_duration_seconds',
    'Text extraction duration in seconds',
    ['file_type']
)

text_quality_score_distribution = Histogram(
    'cvmatic_text_quality_score_distribution',
    'Distribution of text quality scores',
    buckets=[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
)

# NLP processing metrics
nlp_processing_total = Counter(
    'cvmatic_nlp_processing_total',
    'Total NLP processing operations',
    ['language', 'operation', 'status']
)

nlp_processing_duration_seconds = Histogram(
    'cvmatic_nlp_processing_duration_seconds',
    'NLP processing duration in seconds',
    ['language', 'operation']
)

skills_extracted_total = Counter(
    'cvmatic_skills_extracted_total',
    'Total skills extracted',
    ['language', 'category']
)

# Machine learning metrics
ml_predictions_total = Counter(
    'cvmatic_ml_predictions_total',
    'Total ML predictions made',
    ['model_name', 'prediction_type']
)

ml_prediction_confidence_distribution = Histogram(
    'cvmatic_ml_prediction_confidence_distribution',
    'Distribution of ML prediction confidence scores',
    buckets=[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
)

ml_model_accuracy = Gauge(
    'cvmatic_ml_model_accuracy',
    'Current ML model accuracy',
    ['model_name']
)

# Learning system metrics
learning_feedback_total = Counter(
    'cvmatic_learning_feedback_total',
    'Total learning feedback received',
    ['feedback_type', 'rating']
)

learning_model_updates_total = Counter(
    'cvmatic_learning_model_updates_total',
    'Total learning model updates',
    ['model_name', 'update_type']
)

learning_new_categories_total = Counter(
    'cvmatic_learning_new_categories_total',
    'Total new categories discovered',
    ['category_type']
)

learning_patterns_discovered_total = Counter(
    'cvmatic_learning_patterns_discovered_total',
    'Total new patterns discovered',
    ['pattern_type']
)

# System health metrics
system_health_status = Gauge(
    'cvmatic_system_health_status',
    'System health status (1=healthy, 0=unhealthy)',
    ['component']
)

active_connections = Gauge(
    'cvmatic_active_connections',
    'Number of active connections'
)

memory_usage_bytes = Gauge(
    'cvmatic_memory_usage_bytes',
    'Memory usage in bytes',
    ['type']
)

# Error metrics
errors_total = Counter(
    'cvmatic_errors_total',
    'Total errors',
    ['error_type', 'component']
)

# File processing metrics
file_uploads_total = Counter(
    'cvmatic_file_uploads_total',
    'Total file uploads',
    ['file_type', 'size_category']
)

file_processing_errors_total = Counter(
    'cvmatic_file_processing_errors_total',
    'Total file processing errors',
    ['file_type', 'error_type']
)


def track_request_metrics(method: str, endpoint: str):
    """Decorator to track HTTP request metrics"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            status_code = 200
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                status_code = getattr(e, 'status_code', 500)
                raise
            finally:
                duration = time.time() - start_time
                http_requests_total.labels(
                    method=method,
                    endpoint=endpoint,
                    status_code=status_code
                ).inc()
                http_request_duration_seconds.labels(
                    method=method,
                    endpoint=endpoint
                ).observe(duration)
        
        return wrapper
    return decorator


def track_cv_analysis_metrics(language: str, file_type: str):
    """Decorator to track CV analysis metrics"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            status = "success"
            
            try:
                result = await func(*args, **kwargs)
                
                # Track score distribution if result has score
                if hasattr(result, 'score_breakdown') and result.score_breakdown:
                    cv_analysis_score_distribution.observe(result.score_breakdown.overall_score)
                
                return result
            except Exception:
                status = "error"
                raise
            finally:
                duration = time.time() - start_time
                cv_analyses_total.labels(
                    language=language,
                    file_type=file_type,
                    status=status
                ).inc()
                cv_analysis_duration_seconds.labels(
                    language=language,
                    file_type=file_type
                ).observe(duration)
        
        return wrapper
    return decorator


def track_text_extraction_metrics(file_type: str):
    """Decorator to track text extraction metrics"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            status = "success"
            
            try:
                result = await func(*args, **kwargs)
                
                # Track quality score if available
                if isinstance(result, tuple) and len(result) >= 3:
                    quality_score = result[2]  # Assuming quality score is third element
                    text_quality_score_distribution.observe(quality_score)
                
                return result
            except Exception:
                status = "error"
                raise
            finally:
                duration = time.time() - start_time
                text_extraction_total.labels(
                    file_type=file_type,
                    status=status
                ).inc()
                text_extraction_duration_seconds.labels(
                    file_type=file_type
                ).observe(duration)
        
        return wrapper
    return decorator


def track_nlp_metrics(language: str, operation: str):
    """Decorator to track NLP processing metrics"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            status = "success"
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception:
                status = "error"
                raise
            finally:
                duration = time.time() - start_time
                nlp_processing_total.labels(
                    language=language,
                    operation=operation,
                    status=status
                ).inc()
                nlp_processing_duration_seconds.labels(
                    language=language,
                    operation=operation
                ).observe(duration)
        
        return wrapper
    return decorator


def record_ml_prediction(model_name: str, prediction_type: str, confidence: float):
    """Record ML prediction metrics"""
    ml_predictions_total.labels(
        model_name=model_name,
        prediction_type=prediction_type
    ).inc()
    
    ml_prediction_confidence_distribution.observe(confidence)


def record_learning_feedback(feedback_type: str, rating: int):
    """Record learning feedback metrics"""
    learning_feedback_total.labels(
        feedback_type=feedback_type,
        rating=str(rating)
    ).inc()


def record_model_update(model_name: str, update_type: str):
    """Record model update metrics"""
    learning_model_updates_total.labels(
        model_name=model_name,
        update_type=update_type
    ).inc()


def record_new_category(category_type: str):
    """Record new category discovery"""
    learning_new_categories_total.labels(
        category_type=category_type
    ).inc()


def record_error(error_type: str, component: str):
    """Record error metrics"""
    errors_total.labels(
        error_type=error_type,
        component=component
    ).inc()


def update_system_health(component: str, is_healthy: bool):
    """Update system health status"""
    system_health_status.labels(component=component).set(1 if is_healthy else 0)


def record_file_upload(file_type: str, file_size: int):
    """Record file upload metrics"""
    # Categorize file size
    if file_size < 1024 * 1024:  # < 1MB
        size_category = "small"
    elif file_size < 5 * 1024 * 1024:  # < 5MB
        size_category = "medium"
    else:
        size_category = "large"
    
    file_uploads_total.labels(
        file_type=file_type,
        size_category=size_category
    ).inc()


def record_skills_extracted(language: str, category: str, count: int):
    """Record skills extraction metrics"""
    skills_extracted_total.labels(
        language=language,
        category=category
    ).inc(count)
