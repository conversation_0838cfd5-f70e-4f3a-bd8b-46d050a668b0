"""
Health monitoring and system diagnostics
"""

import asyncio
import psutil
import time
from typing import Dict, Any, List
from datetime import datetime, timedelta
import structlog

from app.core.logging import C<PERSON>nalysisLogger
from app.monitoring.metrics import (
    update_system_health, memory_usage_bytes, active_connections
)

logger = CVAnalysisLogger("health_monitor")


class HealthMonitor:
    """System health monitoring and diagnostics"""
    
    def __init__(self):
        self.start_time = time.time()
        self.health_checks = {}
        self.monitoring_active = False
        
    async def start_monitoring(self):
        """Start continuous health monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        # Start monitoring tasks
        asyncio.create_task(self._monitor_system_resources())
        asyncio.create_task(self._monitor_component_health())
        
        logger.logger.info("health_monitoring_started")
    
    async def stop_monitoring(self):
        """Stop health monitoring"""
        self.monitoring_active = False
        logger.logger.info("health_monitoring_stopped")
    
    async def _monitor_system_resources(self):
        """Monitor system resource usage"""
        while self.monitoring_active:
            try:
                # Memory usage
                memory = psutil.virtual_memory()
                memory_usage_bytes.labels(type="used").set(memory.used)
                memory_usage_bytes.labels(type="available").set(memory.available)
                memory_usage_bytes.labels(type="total").set(memory.total)
                
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # Disk usage
                disk = psutil.disk_usage('/')
                
                # Log resource usage
                logger.logger.info(
                    "system_resources",
                    memory_percent=memory.percent,
                    cpu_percent=cpu_percent,
                    disk_percent=(disk.used / disk.total) * 100,
                    uptime_seconds=time.time() - self.start_time
                )
                
                # Update health status based on resource usage
                memory_healthy = memory.percent < 90
                cpu_healthy = cpu_percent < 90
                disk_healthy = (disk.used / disk.total) * 100 < 90
                
                update_system_health("memory", memory_healthy)
                update_system_health("cpu", cpu_healthy)
                update_system_health("disk", disk_healthy)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.log_error(
                    error_type="resource_monitoring_failed",
                    error_message=str(e),
                    context={}
                )
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _monitor_component_health(self):
        """Monitor component health status"""
        while self.monitoring_active:
            try:
                # Check registered health checks
                for component, health_check in self.health_checks.items():
                    try:
                        is_healthy = await health_check()
                        update_system_health(component, is_healthy)
                        
                        logger.logger.debug(
                            "component_health_check",
                            component=component,
                            healthy=is_healthy
                        )
                        
                    except Exception as e:
                        update_system_health(component, False)
                        logger.log_error(
                            error_type="component_health_check_failed",
                            error_message=str(e),
                            context={"component": component}
                        )
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.log_error(
                    error_type="component_monitoring_failed",
                    error_message=str(e),
                    context={}
                )
                await asyncio.sleep(120)  # Wait longer on error
    
    def register_health_check(self, component: str, health_check_func):
        """Register a health check function for a component"""
        self.health_checks[component] = health_check_func
        logger.logger.info("health_check_registered", component=component)
    
    def unregister_health_check(self, component: str):
        """Unregister a health check function"""
        if component in self.health_checks:
            del self.health_checks[component]
            logger.logger.info("health_check_unregistered", component=component)
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health status"""
        try:
            # System resources
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=0.1)
            disk = psutil.disk_usage('/')
            
            # Uptime
            uptime_seconds = time.time() - self.start_time
            uptime_hours = uptime_seconds / 3600
            
            # Component health
            component_health = {}
            for component, health_check in self.health_checks.items():
                try:
                    component_health[component] = await health_check()
                except Exception:
                    component_health[component] = False
            
            # Overall health status
            resource_healthy = (
                memory.percent < 90 and
                cpu_percent < 90 and
                (disk.used / disk.total) * 100 < 90
            )
            
            components_healthy = all(component_health.values()) if component_health else True
            
            overall_healthy = resource_healthy and components_healthy
            
            return {
                "status": "healthy" if overall_healthy else "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "uptime_hours": round(uptime_hours, 2),
                "system_resources": {
                    "memory": {
                        "used_percent": memory.percent,
                        "used_gb": round(memory.used / (1024**3), 2),
                        "total_gb": round(memory.total / (1024**3), 2),
                        "healthy": memory.percent < 90,
                    },
                    "cpu": {
                        "usage_percent": cpu_percent,
                        "healthy": cpu_percent < 90,
                    },
                    "disk": {
                        "used_percent": round((disk.used / disk.total) * 100, 2),
                        "used_gb": round(disk.used / (1024**3), 2),
                        "total_gb": round(disk.total / (1024**3), 2),
                        "healthy": (disk.used / disk.total) * 100 < 90,
                    },
                },
                "components": component_health,
                "monitoring_active": self.monitoring_active,
            }
            
        except Exception as e:
            logger.log_error(
                error_type="health_status_failed",
                error_message=str(e),
                context={}
            )
            return {
                "status": "unknown",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics and statistics"""
        try:
            # System performance
            cpu_times = psutil.cpu_times()
            memory = psutil.virtual_memory()
            
            # Network statistics
            network = psutil.net_io_counters()
            
            # Process information
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "cpu": {
                    "user_time": cpu_times.user,
                    "system_time": cpu_times.system,
                    "idle_time": cpu_times.idle,
                    "current_percent": psutil.cpu_percent(interval=0.1),
                },
                "memory": {
                    "total_bytes": memory.total,
                    "available_bytes": memory.available,
                    "used_bytes": memory.used,
                    "cached_bytes": memory.cached,
                    "process_rss_bytes": process_memory.rss,
                    "process_vms_bytes": process_memory.vms,
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv,
                },
                "uptime_seconds": time.time() - self.start_time,
            }
            
        except Exception as e:
            logger.log_error(
                error_type="performance_metrics_failed",
                error_message=str(e),
                context={}
            )
            return {"error": str(e)}
    
    async def run_diagnostics(self) -> Dict[str, Any]:
        """Run comprehensive system diagnostics"""
        try:
            diagnostics = {
                "timestamp": datetime.utcnow().isoformat(),
                "system_info": {
                    "platform": psutil.LINUX if hasattr(psutil, 'LINUX') else "unknown",
                    "cpu_count": psutil.cpu_count(),
                    "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat(),
                },
                "health_status": await self.get_system_health(),
                "performance_metrics": await self.get_performance_metrics(),
                "component_diagnostics": {},
            }
            
            # Run component-specific diagnostics
            for component, health_check in self.health_checks.items():
                try:
                    # Run health check and measure response time
                    start_time = time.time()
                    is_healthy = await health_check()
                    response_time = time.time() - start_time
                    
                    diagnostics["component_diagnostics"][component] = {
                        "healthy": is_healthy,
                        "response_time_ms": round(response_time * 1000, 2),
                    }
                    
                except Exception as e:
                    diagnostics["component_diagnostics"][component] = {
                        "healthy": False,
                        "error": str(e),
                    }
            
            return diagnostics
            
        except Exception as e:
            logger.log_error(
                error_type="diagnostics_failed",
                error_message=str(e),
                context={}
            )
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }


# Global health monitor instance
health_monitor = HealthMonitor()
