"""
Adaptive classification system that learns new categories and patterns
"""

import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import DBSCAN, KMeans
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import PCA
import joblib
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import structlog
from pathlib import Path

from app.core.config import settings
from app.core.exceptions import MLModelError
from app.core.logging import CVAnalysisLogger
from app.models.cv_analysis import Skill, SkillCategoryEnum

logger = CVAnalysisLogger("adaptive_classifier")


class AdaptiveSkillClassifier:
    """Adaptive classifier that learns new skill categories and patterns"""
    
    def __init__(self):
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            ngram_range=(1, 3),
            stop_words='english',
            min_df=2,
            max_df=0.8
        )
        
        self.classifier = RandomForestClassifier(
            n_estimators=100,
            random_state=42,
            class_weight='balanced'
        )
        
        self.clustering_model = DBSCAN(eps=0.3, min_samples=3)
        self.pca = PCA(n_components=50)
        
        # Category management
        self.known_categories = set(SkillCategoryEnum)
        self.category_patterns = {}
        self.skill_embeddings = {}
        
        # Learning parameters
        self.min_samples_for_new_category = 5
        self.similarity_threshold = 0.7
        self.confidence_threshold = 0.6
        
        # Model paths
        self.model_dir = Path(settings.ML_MODELS_DIR)
        self.model_dir.mkdir(exist_ok=True)
        
        self.vectorizer_path = self.model_dir / "skill_vectorizer.joblib"
        self.classifier_path = self.model_dir / "skill_classifier.joblib"
        self.categories_path = self.model_dir / "categories.json"
        self.embeddings_path = self.model_dir / "skill_embeddings.json"
        
        # Training data
        self.training_data = []
        self.feedback_data = []
        
    async def initialize(self):
        """Initialize the classifier with existing models or create new ones"""
        try:
            await self._load_existing_models()
            await self._load_base_categories()
            logger.logger.info("adaptive_classifier_initialized")
            
        except Exception as e:
            logger.log_error(
                error_type="classifier_initialization_failed",
                error_message=str(e),
                context={}
            )
            # Initialize with base categories if loading fails
            await self._initialize_base_categories()
    
    async def classify_skill(self, skill_text: str, context: str = "") -> Tuple[str, float]:
        """
        Classify a skill and return category with confidence
        
        Args:
            skill_text: The skill to classify
            context: Context where the skill was found
            
        Returns:
            Tuple of (category, confidence)
        """
        try:
            # Prepare input
            input_text = f"{skill_text} {context}".strip()
            
            # Check if we have a trained classifier
            if hasattr(self.classifier, 'classes_'):
                # Use trained classifier
                features = self.vectorizer.transform([input_text])
                
                if features.shape[1] > 0:
                    probabilities = self.classifier.predict_proba(features)[0]
                    predicted_class_idx = np.argmax(probabilities)
                    confidence = probabilities[predicted_class_idx]
                    category = self.classifier.classes_[predicted_class_idx]
                    
                    if confidence >= self.confidence_threshold:
                        return category, confidence
            
            # Fallback to pattern matching
            category = await self._classify_by_patterns(skill_text)
            return category, 0.8  # Pattern-based confidence
            
        except Exception as e:
            logger.log_error(
                error_type="skill_classification_failed",
                error_message=str(e),
                context={"skill": skill_text}
            )
            return "other", 0.5
    
    async def learn_from_feedback(self, skill: str, correct_category: str, context: str = ""):
        """Learn from user feedback"""
        feedback_entry = {
            "skill": skill,
            "category": correct_category,
            "context": context,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        self.feedback_data.append(feedback_entry)
        
        # Trigger retraining if we have enough feedback
        if len(self.feedback_data) >= settings.FEEDBACK_LEARNING_THRESHOLD:
            await self._retrain_with_feedback()
    
    async def discover_new_categories(self, skills_data: List[Dict[str, Any]]) -> List[str]:
        """
        Discover new skill categories using clustering
        
        Args:
            skills_data: List of skill dictionaries with text and context
            
        Returns:
            List of newly discovered category names
        """
        try:
            if len(skills_data) < self.min_samples_for_new_category:
                return []
            
            # Prepare data for clustering
            texts = [f"{item['skill']} {item.get('context', '')}" for item in skills_data]
            
            # Vectorize
            if not hasattr(self.vectorizer, 'vocabulary_'):
                # Fit vectorizer if not already fitted
                self.vectorizer.fit(texts)
            
            features = self.vectorizer.transform(texts)
            
            # Apply PCA for dimensionality reduction
            if features.shape[1] > 50:
                if not hasattr(self.pca, 'components_'):
                    self.pca.fit(features.toarray())
                features_reduced = self.pca.transform(features.toarray())
            else:
                features_reduced = features.toarray()
            
            # Perform clustering
            clusters = self.clustering_model.fit_predict(features_reduced)
            
            # Analyze clusters for new categories
            new_categories = []
            unique_clusters = set(clusters)
            unique_clusters.discard(-1)  # Remove noise cluster
            
            for cluster_id in unique_clusters:
                cluster_skills = [skills_data[i] for i, c in enumerate(clusters) if c == cluster_id]
                
                if len(cluster_skills) >= self.min_samples_for_new_category:
                    # Generate category name for this cluster
                    category_name = await self._generate_category_name(cluster_skills)
                    
                    if category_name and category_name not in self.known_categories:
                        new_categories.append(category_name)
                        await self._add_new_category(category_name, cluster_skills)
            
            logger.log_learning_event(
                event_type="new_categories_discovered",
                feedback_count=len(skills_data),
                model_updated=len(new_categories) > 0,
            )
            
            return new_categories
            
        except Exception as e:
            logger.log_error(
                error_type="category_discovery_failed",
                error_message=str(e),
                context={"skills_count": len(skills_data)}
            )
            return []
    
    async def update_skill_patterns(self, new_patterns: Dict[str, List[str]]):
        """Update skill patterns with new discoveries"""
        for category, patterns in new_patterns.items():
            if category not in self.category_patterns:
                self.category_patterns[category] = []
            
            # Add new patterns that don't already exist
            existing_patterns = set(self.category_patterns[category])
            new_unique_patterns = [p for p in patterns if p not in existing_patterns]
            
            self.category_patterns[category].extend(new_unique_patterns)
        
        # Save updated patterns
        await self._save_categories()
    
    async def get_category_insights(self) -> Dict[str, Any]:
        """Get insights about learned categories and patterns"""
        insights = {
            "total_categories": len(self.known_categories),
            "custom_categories": len(self.known_categories) - len(SkillCategoryEnum),
            "total_patterns": sum(len(patterns) for patterns in self.category_patterns.values()),
            "feedback_samples": len(self.feedback_data),
            "last_training": getattr(self, 'last_training_date', None),
            "model_accuracy": getattr(self, 'model_accuracy', None),
        }
        
        # Category distribution
        category_distribution = {}
        for category in self.known_categories:
            pattern_count = len(self.category_patterns.get(category, []))
            category_distribution[category] = pattern_count
        
        insights["category_distribution"] = category_distribution
        
        return insights
    
    async def _load_existing_models(self):
        """Load existing trained models"""
        if self.vectorizer_path.exists():
            self.vectorizer = joblib.load(self.vectorizer_path)
            
        if self.classifier_path.exists():
            self.classifier = joblib.load(self.classifier_path)
            
        if self.categories_path.exists():
            with open(self.categories_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.category_patterns = data.get('patterns', {})
                self.known_categories.update(data.get('categories', []))
                
        if self.embeddings_path.exists():
            with open(self.embeddings_path, 'r', encoding='utf-8') as f:
                self.skill_embeddings = json.load(f)
    
    async def _load_base_categories(self):
        """Load base skill categories and patterns"""
        base_patterns = {
            "technical": [
                "programming", "software", "development", "coding", "algorithm",
                "database", "framework", "library", "api", "backend", "frontend",
                "python", "java", "javascript", "react", "angular", "node",
                "sql", "nosql", "mongodb", "postgresql", "mysql",
                "aws", "azure", "docker", "kubernetes", "git", "linux"
            ],
            "soft": [
                "communication", "leadership", "teamwork", "collaboration",
                "problem solving", "critical thinking", "creativity", "adaptability",
                "time management", "project management", "negotiation"
            ],
            "language": [
                "english", "hebrew", "arabic", "french", "german", "spanish",
                "native", "fluent", "intermediate", "basic", "bilingual"
            ],
            "tool": [
                "excel", "powerpoint", "word", "photoshop", "illustrator",
                "figma", "sketch", "jira", "confluence", "slack", "trello"
            ],
            "certification": [
                "certified", "certification", "license", "accredited",
                "aws certified", "microsoft certified", "google certified",
                "pmp", "cissp", "cisa", "cism"
            ]
        }
        
        self.category_patterns.update(base_patterns)
    
    async def _initialize_base_categories(self):
        """Initialize with base categories if no existing models"""
        await self._load_base_categories()
        await self._save_categories()
        logger.logger.info("base_categories_initialized")
    
    async def _classify_by_patterns(self, skill_text: str) -> str:
        """Classify skill using pattern matching"""
        skill_lower = skill_text.lower()
        
        # Check each category's patterns
        for category, patterns in self.category_patterns.items():
            for pattern in patterns:
                if pattern.lower() in skill_lower:
                    return category
        
        return "other"
    
    async def _retrain_with_feedback(self):
        """Retrain the classifier with accumulated feedback"""
        try:
            if len(self.feedback_data) < 10:  # Need minimum samples
                return
            
            # Prepare training data
            texts = []
            labels = []
            
            for feedback in self.feedback_data:
                text = f"{feedback['skill']} {feedback.get('context', '')}"
                texts.append(text)
                labels.append(feedback['category'])
            
            # Fit vectorizer and classifier
            features = self.vectorizer.fit_transform(texts)
            self.classifier.fit(features, labels)
            
            # Save updated models
            await self._save_models()
            
            # Clear feedback data
            self.feedback_data = []
            self.last_training_date = datetime.utcnow()
            
            logger.log_learning_event(
                event_type="model_retrained",
                feedback_count=len(texts),
                model_updated=True,
            )
            
        except Exception as e:
            logger.log_error(
                error_type="retraining_failed",
                error_message=str(e),
                context={"feedback_count": len(self.feedback_data)}
            )
    
    async def _generate_category_name(self, cluster_skills: List[Dict[str, Any]]) -> Optional[str]:
        """Generate a meaningful name for a new category"""
        # Extract common words from cluster skills
        all_words = []
        for skill_data in cluster_skills:
            words = skill_data['skill'].lower().split()
            all_words.extend(words)
        
        # Find most common meaningful words
        word_counts = {}
        for word in all_words:
            if len(word) > 3 and word.isalpha():  # Filter short and non-alphabetic words
                word_counts[word] = word_counts.get(word, 0) + 1
        
        if not word_counts:
            return None
        
        # Get most common word as category base
        most_common_word = max(word_counts, key=word_counts.get)
        
        # Generate category name
        if word_counts[most_common_word] >= len(cluster_skills) * 0.5:
            return f"{most_common_word}_skills"
        
        return None
    
    async def _add_new_category(self, category_name: str, cluster_skills: List[Dict[str, Any]]):
        """Add a new category with its patterns"""
        self.known_categories.add(category_name)
        
        # Extract patterns from cluster skills
        patterns = []
        for skill_data in cluster_skills:
            skill_words = skill_data['skill'].lower().split()
            patterns.extend(skill_words)
        
        # Remove duplicates and short words
        unique_patterns = list(set(word for word in patterns if len(word) > 2))
        self.category_patterns[category_name] = unique_patterns
        
        await self._save_categories()
    
    async def _save_models(self):
        """Save trained models"""
        joblib.dump(self.vectorizer, self.vectorizer_path)
        joblib.dump(self.classifier, self.classifier_path)
    
    async def _save_categories(self):
        """Save categories and patterns"""
        data = {
            "categories": list(self.known_categories),
            "patterns": self.category_patterns,
            "last_updated": datetime.utcnow().isoformat(),
        }
        
        with open(self.categories_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)


class PatternRecognizer:
    """Recognizes new patterns in CV data"""

    def __init__(self):
        self.pattern_history = []
        self.skill_combinations = {}
        self.structure_templates = []

    async def detect_new_patterns(self, cv_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect new patterns in CV data"""
        patterns = []

        # Analyze structure patterns
        structure_pattern = await self._analyze_structure_pattern(cv_data)
        if structure_pattern:
            patterns.append(structure_pattern)

        # Analyze skill combination patterns
        skill_pattern = await self._analyze_skill_patterns(cv_data.get('skills', []))
        if skill_pattern:
            patterns.append(skill_pattern)

        # Analyze experience patterns
        exp_pattern = await self._analyze_experience_patterns(cv_data.get('experience', []))
        if exp_pattern:
            patterns.append(exp_pattern)

        return patterns

    async def _analyze_structure_pattern(self, cv_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze CV structure patterns"""
        structure = {
            "has_summary": bool(cv_data.get('personal_info', {}).get('summary')),
            "skills_count": len(cv_data.get('skills', [])),
            "experience_count": len(cv_data.get('experience', [])),
            "education_count": len(cv_data.get('education', [])),
            "languages_count": len(cv_data.get('languages', [])),
            "certifications_count": len(cv_data.get('certifications', [])),
        }

        # Check if this is a new structure pattern
        if not self._is_known_structure(structure):
            return {
                "type": "structure",
                "pattern": structure,
                "novelty_score": self._calculate_structure_novelty(structure),
            }

        return None

    async def _analyze_skill_patterns(self, skills: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Analyze skill combination patterns"""
        if len(skills) < 3:
            return None

        # Extract skill categories
        categories = [skill.get('category', 'other') for skill in skills]
        category_combo = tuple(sorted(set(categories)))

        # Check if this combination is new
        if category_combo not in self.skill_combinations:
            self.skill_combinations[category_combo] = 1
            return {
                "type": "skill_combination",
                "pattern": category_combo,
                "skills": [skill.get('name') for skill in skills],
                "novelty_score": 0.8,
            }
        else:
            self.skill_combinations[category_combo] += 1

        return None

    async def _analyze_experience_patterns(self, experiences: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Analyze experience patterns"""
        if not experiences:
            return None

        # Analyze career progression patterns
        positions = [exp.get('position', '') for exp in experiences]
        companies = [exp.get('company', '') for exp in experiences]

        # Look for unique career paths
        career_pattern = {
            "position_count": len(positions),
            "company_changes": len(set(companies)),
            "avg_tenure": self._calculate_avg_tenure(experiences),
        }

        if self._is_unique_career_pattern(career_pattern):
            return {
                "type": "career_progression",
                "pattern": career_pattern,
                "positions": positions,
                "novelty_score": 0.7,
            }

        return None

    def _is_known_structure(self, structure: Dict[str, Any]) -> bool:
        """Check if structure pattern is already known"""
        for template in self.structure_templates:
            similarity = self._calculate_structure_similarity(structure, template)
            if similarity > 0.8:
                return True
        return False

    def _calculate_structure_novelty(self, structure: Dict[str, Any]) -> float:
        """Calculate novelty score for structure"""
        if not self.structure_templates:
            self.structure_templates.append(structure)
            return 1.0

        max_similarity = max(
            self._calculate_structure_similarity(structure, template)
            for template in self.structure_templates
        )

        novelty = 1.0 - max_similarity
        if novelty > 0.3:  # Significant novelty
            self.structure_templates.append(structure)

        return novelty

    def _calculate_structure_similarity(self, struct1: Dict[str, Any], struct2: Dict[str, Any]) -> float:
        """Calculate similarity between two structures"""
        common_keys = set(struct1.keys()) & set(struct2.keys())
        if not common_keys:
            return 0.0

        similarities = []
        for key in common_keys:
            if isinstance(struct1[key], bool) and isinstance(struct2[key], bool):
                similarities.append(1.0 if struct1[key] == struct2[key] else 0.0)
            elif isinstance(struct1[key], (int, float)) and isinstance(struct2[key], (int, float)):
                max_val = max(struct1[key], struct2[key], 1)
                similarities.append(1.0 - abs(struct1[key] - struct2[key]) / max_val)

        return sum(similarities) / len(similarities) if similarities else 0.0

    def _calculate_avg_tenure(self, experiences: List[Dict[str, Any]]) -> float:
        """Calculate average tenure from experiences"""
        tenures = []
        for exp in experiences:
            duration = exp.get('duration_months', 0)
            if duration > 0:
                tenures.append(duration)

        return sum(tenures) / len(tenures) if tenures else 0.0

    def _is_unique_career_pattern(self, pattern: Dict[str, Any]) -> bool:
        """Check if career pattern is unique"""
        # Simple heuristic - can be made more sophisticated
        return (
            pattern['position_count'] > 5 or
            pattern['company_changes'] > 3 or
            pattern['avg_tenure'] > 60  # More than 5 years average
        )
