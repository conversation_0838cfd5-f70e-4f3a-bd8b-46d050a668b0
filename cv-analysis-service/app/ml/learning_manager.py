"""
Learning and feedback management system
"""

import json
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
from pathlib import Path
import structlog

from app.core.config import settings
from app.core.logging import <PERSON><PERSON><PERSON>ysisLogger
from app.ml.adaptive_classifier import AdaptiveSkillClassifier, Pat<PERSON><PERSON><PERSON>ognizer
from app.ml.cv_scorer import <PERSON>VScorer
from app.models.cv_analysis import <PERSON><PERSON><PERSON><PERSON>, FeedbackData

logger = CVAnalysisLogger("learning_manager")


class LearningManager:
    """Manages continuous learning and model improvement"""
    
    def __init__(self):
        self.adaptive_classifier = AdaptiveSkillClassifier()
        self.pattern_recognizer = PatternRecognizer()
        self.cv_scorer = CVScorer()
        
        # Learning data storage
        self.feedback_storage = Path(settings.ML_MODELS_DIR) / "feedback"
        self.feedback_storage.mkdir(exist_ok=True)
        
        self.learning_metrics = {
            "total_cvs_processed": 0,
            "feedback_received": 0,
            "models_updated": 0,
            "new_categories_discovered": 0,
            "accuracy_improvements": [],
        }
        
        # Learning thresholds
        self.min_feedback_for_update = 10
        self.learning_batch_size = 50
        self.pattern_discovery_threshold = 20
        
    async def initialize(self):
        """Initialize learning components"""
        try:
            await self.adaptive_classifier.initialize()
            await self._load_learning_metrics()
            
            # Start background learning tasks
            asyncio.create_task(self._periodic_learning_cycle())
            
            logger.logger.info("learning_manager_initialized")
            
        except Exception as e:
            logger.log_error(
                error_type="learning_manager_init_failed",
                error_message=str(e),
                context={}
            )
    
    async def process_cv_analysis(self, cv_content: CVContent, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Process CV analysis for learning opportunities"""
        try:
            # Update processing metrics
            self.learning_metrics["total_cvs_processed"] += 1
            
            # Discover new patterns
            new_patterns = await self.pattern_recognizer.detect_new_patterns(analysis_result)
            
            if new_patterns:
                await self._handle_new_patterns(new_patterns, cv_content)
            
            # Check for new skill categories
            skills_data = [
                {
                    "skill": skill.name,
                    "category": skill.category,
                    "context": f"Found in {cv_content.personal_info.full_name or 'CV'}"
                }
                for skill in cv_content.skills
            ]
            
            if len(skills_data) >= 5:  # Minimum skills for category discovery
                new_categories = await self.adaptive_classifier.discover_new_categories(skills_data)
                if new_categories:
                    self.learning_metrics["new_categories_discovered"] += len(new_categories)
                    logger.log_learning_event(
                        event_type="new_categories_discovered",
                        feedback_count=len(skills_data),
                        model_updated=True,
                    )
            
            # Store analysis for future learning
            await self._store_analysis_data(cv_content, analysis_result)
            
            return {
                "learning_insights": {
                    "new_patterns_found": len(new_patterns),
                    "new_categories_discovered": len(new_categories) if 'new_categories' in locals() else 0,
                    "total_cvs_processed": self.learning_metrics["total_cvs_processed"],
                }
            }
            
        except Exception as e:
            logger.log_error(
                error_type="cv_analysis_processing_failed",
                error_message=str(e),
                context={"cv_id": getattr(cv_content, 'id', 'unknown')}
            )
            return {"learning_insights": {}}
    
    async def receive_feedback(self, feedback_data: FeedbackData) -> Dict[str, Any]:
        """Process user feedback for learning"""
        try:
            # Store feedback
            await self._store_feedback(feedback_data)
            self.learning_metrics["feedback_received"] += 1
            
            # Process skill corrections
            if feedback_data.skill_corrections:
                for correction in feedback_data.skill_corrections:
                    await self.adaptive_classifier.learn_from_feedback(
                        skill=correction.skill_name,
                        correct_category=correction.correct_category,
                        context=correction.context or ""
                    )
            
            # Process scoring feedback
            if feedback_data.score_feedback:
                await self._process_score_feedback(feedback_data.score_feedback)
            
            # Check if we should trigger model updates
            if self.learning_metrics["feedback_received"] % self.min_feedback_for_update == 0:
                await self._trigger_model_update()
            
            logger.log_learning_event(
                event_type="feedback_received",
                feedback_count=1,
                model_updated=False,
            )
            
            return {
                "feedback_processed": True,
                "total_feedback_received": self.learning_metrics["feedback_received"],
                "next_model_update_in": self.min_feedback_for_update - (self.learning_metrics["feedback_received"] % self.min_feedback_for_update)
            }
            
        except Exception as e:
            logger.log_error(
                error_type="feedback_processing_failed",
                error_message=str(e),
                context={"feedback_type": feedback_data.feedback_type}
            )
            return {"feedback_processed": False}
    
    async def get_learning_insights(self) -> Dict[str, Any]:
        """Get comprehensive learning insights and metrics"""
        try:
            # Get classifier insights
            classifier_insights = await self.adaptive_classifier.get_category_insights()
            
            # Calculate learning velocity
            learning_velocity = await self._calculate_learning_velocity()
            
            # Get recent improvements
            recent_improvements = await self._get_recent_improvements()
            
            insights = {
                "processing_metrics": self.learning_metrics,
                "classifier_insights": classifier_insights,
                "learning_velocity": learning_velocity,
                "recent_improvements": recent_improvements,
                "system_health": {
                    "models_loaded": True,
                    "feedback_processing": True,
                    "pattern_recognition": True,
                },
                "recommendations": await self._get_system_recommendations(),
            }
            
            return insights
            
        except Exception as e:
            logger.log_error(
                error_type="learning_insights_failed",
                error_message=str(e),
                context={}
            )
            return {"error": "Failed to generate learning insights"}
    
    async def force_model_update(self) -> Dict[str, Any]:
        """Force immediate model update with available data"""
        try:
            result = await self._trigger_model_update()
            
            logger.log_learning_event(
                event_type="forced_model_update",
                feedback_count=self.learning_metrics["feedback_received"],
                model_updated=result["updated"],
            )
            
            return result
            
        except Exception as e:
            logger.log_error(
                error_type="forced_update_failed",
                error_message=str(e),
                context={}
            )
            return {"updated": False, "error": str(e)}
    
    async def _handle_new_patterns(self, patterns: List[Dict[str, Any]], cv_content: CVContent):
        """Handle newly discovered patterns"""
        for pattern in patterns:
            pattern_data = {
                "pattern": pattern,
                "cv_source": cv_content.personal_info.full_name or "anonymous",
                "timestamp": datetime.utcnow().isoformat(),
                "novelty_score": pattern.get("novelty_score", 0.5),
            }
            
            # Store pattern for analysis
            pattern_file = self.feedback_storage / f"pattern_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
            with open(pattern_file, 'w', encoding='utf-8') as f:
                json.dump(pattern_data, f, ensure_ascii=False, indent=2)
    
    async def _store_analysis_data(self, cv_content: CVContent, analysis_result: Dict[str, Any]):
        """Store analysis data for future learning"""
        analysis_data = {
            "cv_summary": {
                "skills_count": len(cv_content.skills),
                "experience_count": len(cv_content.experience),
                "education_count": len(cv_content.education),
                "languages_count": len(cv_content.languages),
            },
            "analysis_result": {
                "overall_score": analysis_result.get("score_breakdown", {}).get("overall_score"),
                "experience_level": analysis_result.get("experience_level"),
                "recommendations_count": len(analysis_result.get("recommendations", [])),
            },
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        # Store in daily batch file
        date_str = datetime.utcnow().strftime('%Y%m%d')
        batch_file = self.feedback_storage / f"analysis_batch_{date_str}.jsonl"
        
        with open(batch_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(analysis_data, ensure_ascii=False) + '\n')
    
    async def _store_feedback(self, feedback_data: FeedbackData):
        """Store feedback data"""
        feedback_record = {
            "feedback_type": feedback_data.feedback_type,
            "cv_id": feedback_data.cv_id,
            "user_id": feedback_data.user_id,
            "skill_corrections": [
                {
                    "skill_name": correction.skill_name,
                    "original_category": correction.original_category,
                    "correct_category": correction.correct_category,
                    "context": correction.context,
                }
                for correction in (feedback_data.skill_corrections or [])
            ],
            "score_feedback": feedback_data.score_feedback,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        feedback_file = self.feedback_storage / f"feedback_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        with open(feedback_file, 'w', encoding='utf-8') as f:
            json.dump(feedback_record, f, ensure_ascii=False, indent=2)
    
    async def _process_score_feedback(self, score_feedback: Dict[str, Any]):
        """Process scoring feedback for model improvement"""
        # This could be used to adjust scoring weights or thresholds
        # For now, we just log it for future analysis
        logger.logger.info(
            "score_feedback_received",
            feedback=score_feedback,
            timestamp=datetime.utcnow().isoformat()
        )
    
    async def _trigger_model_update(self) -> Dict[str, Any]:
        """Trigger model update with accumulated feedback"""
        try:
            # Load recent feedback
            feedback_files = list(self.feedback_storage.glob("feedback_*.json"))
            recent_feedback = []
            
            cutoff_date = datetime.utcnow() - timedelta(days=7)  # Last week's feedback
            
            for feedback_file in feedback_files:
                try:
                    with open(feedback_file, 'r', encoding='utf-8') as f:
                        feedback = json.load(f)
                        feedback_date = datetime.fromisoformat(feedback['timestamp'])
                        
                        if feedback_date > cutoff_date:
                            recent_feedback.append(feedback)
                except Exception:
                    continue
            
            if len(recent_feedback) < self.min_feedback_for_update:
                return {"updated": False, "reason": "Insufficient feedback data"}
            
            # Update models with feedback
            updates_made = 0
            
            # Update classifier with skill corrections
            skill_corrections = []
            for feedback in recent_feedback:
                skill_corrections.extend(feedback.get('skill_corrections', []))
            
            if skill_corrections:
                for correction in skill_corrections:
                    await self.adaptive_classifier.learn_from_feedback(
                        skill=correction['skill_name'],
                        correct_category=correction['correct_category'],
                        context=correction.get('context', '')
                    )
                updates_made += 1
            
            self.learning_metrics["models_updated"] += updates_made
            
            return {
                "updated": updates_made > 0,
                "updates_made": updates_made,
                "feedback_processed": len(recent_feedback),
            }
            
        except Exception as e:
            logger.log_error(
                error_type="model_update_failed",
                error_message=str(e),
                context={}
            )
            return {"updated": False, "error": str(e)}

    async def _load_learning_metrics(self):
        """Load existing learning metrics"""
        metrics_file = self.feedback_storage / "learning_metrics.json"

        if metrics_file.exists():
            try:
                with open(metrics_file, 'r', encoding='utf-8') as f:
                    saved_metrics = json.load(f)
                    self.learning_metrics.update(saved_metrics)
            except Exception as e:
                logger.log_error(
                    error_type="metrics_load_failed",
                    error_message=str(e),
                    context={}
                )

    async def _save_learning_metrics(self):
        """Save current learning metrics"""
        metrics_file = self.feedback_storage / "learning_metrics.json"

        try:
            with open(metrics_file, 'w', encoding='utf-8') as f:
                json.dump(self.learning_metrics, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.log_error(
                error_type="metrics_save_failed",
                error_message=str(e),
                context={}
            )

    async def _periodic_learning_cycle(self):
        """Periodic learning and optimization cycle"""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour

                # Check if we have enough data for learning
                if self.learning_metrics["total_cvs_processed"] % self.pattern_discovery_threshold == 0:
                    await self._analyze_accumulated_patterns()

                # Save metrics periodically
                await self._save_learning_metrics()

                logger.logger.info(
                    "periodic_learning_cycle_completed",
                    metrics=self.learning_metrics
                )

            except Exception as e:
                logger.log_error(
                    error_type="periodic_learning_failed",
                    error_message=str(e),
                    context={}
                )
                await asyncio.sleep(300)  # Wait 5 minutes before retry

    async def _analyze_accumulated_patterns(self):
        """Analyze accumulated patterns for insights"""
        try:
            pattern_files = list(self.feedback_storage.glob("pattern_*.json"))

            if len(pattern_files) < 5:
                return

            patterns = []
            for pattern_file in pattern_files[-20:]:  # Analyze last 20 patterns
                try:
                    with open(pattern_file, 'r', encoding='utf-8') as f:
                        pattern_data = json.load(f)
                        patterns.append(pattern_data)
                except Exception:
                    continue

            # Analyze pattern trends
            pattern_types = {}
            for pattern_data in patterns:
                pattern_type = pattern_data['pattern'].get('type', 'unknown')
                pattern_types[pattern_type] = pattern_types.get(pattern_type, 0) + 1

            # Log insights
            logger.logger.info(
                "pattern_analysis_completed",
                pattern_types=pattern_types,
                total_patterns=len(patterns)
            )

        except Exception as e:
            logger.log_error(
                error_type="pattern_analysis_failed",
                error_message=str(e),
                context={}
            )

    async def _calculate_learning_velocity(self) -> Dict[str, Any]:
        """Calculate learning velocity metrics"""
        try:
            # Calculate processing rate
            total_processed = self.learning_metrics["total_cvs_processed"]
            feedback_received = self.learning_metrics["feedback_received"]

            feedback_rate = feedback_received / max(total_processed, 1)

            # Calculate improvement rate
            improvements = self.learning_metrics.get("accuracy_improvements", [])
            recent_improvements = [
                imp for imp in improvements
                if datetime.fromisoformat(imp.get('timestamp', '2000-01-01')) > datetime.utcnow() - timedelta(days=30)
            ]

            return {
                "cvs_per_day": total_processed / max((datetime.utcnow() - datetime(2024, 1, 1)).days, 1),
                "feedback_rate": feedback_rate,
                "recent_improvements": len(recent_improvements),
                "learning_efficiency": feedback_rate * len(recent_improvements),
            }

        except Exception as e:
            logger.log_error(
                error_type="velocity_calculation_failed",
                error_message=str(e),
                context={}
            )
            return {"error": "Failed to calculate learning velocity"}

    async def _get_recent_improvements(self) -> List[Dict[str, Any]]:
        """Get recent system improvements"""
        improvements = []

        # Check for recent model updates
        if self.learning_metrics["models_updated"] > 0:
            improvements.append({
                "type": "model_update",
                "description": f"Updated models {self.learning_metrics['models_updated']} times",
                "impact": "improved_accuracy",
                "timestamp": datetime.utcnow().isoformat(),
            })

        # Check for new categories
        if self.learning_metrics["new_categories_discovered"] > 0:
            improvements.append({
                "type": "category_expansion",
                "description": f"Discovered {self.learning_metrics['new_categories_discovered']} new skill categories",
                "impact": "expanded_coverage",
                "timestamp": datetime.utcnow().isoformat(),
            })

        return improvements[-10:]  # Return last 10 improvements

    async def _get_system_recommendations(self) -> List[Dict[str, Any]]:
        """Get recommendations for system improvement"""
        recommendations = []

        # Check feedback rate
        total_processed = self.learning_metrics["total_cvs_processed"]
        feedback_received = self.learning_metrics["feedback_received"]

        if total_processed > 100 and feedback_received / total_processed < 0.1:
            recommendations.append({
                "type": "feedback_collection",
                "priority": "medium",
                "description": "Consider implementing more feedback collection mechanisms to improve learning rate",
                "action": "Add user feedback prompts after CV analysis",
            })

        # Check model update frequency
        if feedback_received > 50 and self.learning_metrics["models_updated"] == 0:
            recommendations.append({
                "type": "model_training",
                "priority": "high",
                "description": "Sufficient feedback available for model retraining",
                "action": "Trigger model update with accumulated feedback",
            })

        # Check pattern discovery
        if total_processed > 200 and self.learning_metrics["new_categories_discovered"] == 0:
            recommendations.append({
                "type": "pattern_analysis",
                "priority": "low",
                "description": "Consider adjusting pattern discovery sensitivity",
                "action": "Review pattern recognition thresholds",
            })

        return recommendations
