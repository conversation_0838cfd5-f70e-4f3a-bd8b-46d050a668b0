"""
Comprehensive CV scoring system with recommendations
"""

import re
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
import structlog

from app.models.cv_analysis import (
    CVContent, ScoreBreakdown, Recommendation, ExperienceLevelEnum,
    PersonalInfo, Skill, Experience, Education
)
from app.core.logging import CVAnalysisLogger

logger = CVAnalysisLogger("cv_scorer")


class CVScorer:
    """Advanced CV scoring system with multilingual support"""
    
    def __init__(self):
        # Scoring weights
        self.weights = {
            "completeness": 0.25,
            "relevance": 0.20,
            "experience": 0.20,
            "skills": 0.15,
            "education": 0.10,
            "presentation": 0.10,
        }
        
        # Industry-specific skill weights
        self.industry_skills = {
            "technology": {
                "technical": 0.4,
                "soft": 0.3,
                "tool": 0.2,
                "certification": 0.1,
            },
            "marketing": {
                "soft": 0.4,
                "tool": 0.3,
                "technical": 0.2,
                "certification": 0.1,
            },
            "finance": {
                "certification": 0.4,
                "technical": 0.3,
                "soft": 0.2,
                "tool": 0.1,
            },
        }
        
        # Experience level thresholds (in months)
        self.experience_thresholds = {
            ExperienceLevelEnum.ENTRY: (0, 12),
            ExperienceLevelEnum.JUNIOR: (12, 36),
            ExperienceLevelEnum.MID: (36, 84),
            ExperienceLevelEnum.SENIOR: (84, 180),
            ExperienceLevelEnum.LEAD: (180, 300),
            ExperienceLevelEnum.EXECUTIVE: (300, float('inf')),
        }
    
    async def score_cv(
        self,
        cv_content: CVContent,
        target_position: str = None,
        target_industry: str = None,
    ) -> Tuple[ScoreBreakdown, List[Recommendation], ExperienceLevelEnum]:
        """
        Comprehensive CV scoring with recommendations
        
        Args:
            cv_content: Extracted CV content
            target_position: Target job position
            target_industry: Target industry
            
        Returns:
            Tuple of (score_breakdown, recommendations, experience_level)
        """
        try:
            # Calculate individual scores
            completeness_score = await self._score_completeness(cv_content)
            relevance_score = await self._score_relevance(cv_content, target_position, target_industry)
            experience_score = await self._score_experience(cv_content)
            skills_score = await self._score_skills(cv_content, target_industry)
            education_score = await self._score_education(cv_content)
            presentation_score = await self._score_presentation(cv_content)
            
            # Calculate overall score
            overall_score = (
                completeness_score * self.weights["completeness"] +
                relevance_score * self.weights["relevance"] +
                experience_score * self.weights["experience"] +
                skills_score * self.weights["skills"] +
                education_score * self.weights["education"] +
                presentation_score * self.weights["presentation"]
            )
            
            # Create score breakdown
            score_breakdown = ScoreBreakdown(
                overall_score=round(overall_score, 1),
                completeness_score=round(completeness_score, 1),
                relevance_score=round(relevance_score, 1),
                experience_score=round(experience_score, 1),
                skills_score=round(skills_score, 1),
                education_score=round(education_score, 1),
                presentation_score=round(presentation_score, 1),
            )
            
            # Determine experience level
            experience_level = await self._determine_experience_level(cv_content)
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(
                cv_content, score_breakdown, target_position, target_industry
            )
            
            logger.log_ml_prediction(
                model_name="cv_scorer",
                confidence=overall_score / 100,
                categories_predicted=len(recommendations),
            )
            
            return score_breakdown, recommendations, experience_level
            
        except Exception as e:
            logger.log_error(
                error_type="cv_scoring_failed",
                error_message=str(e),
                context={"target_position": target_position, "target_industry": target_industry}
            )
            # Return default scores on error
            default_score = ScoreBreakdown(
                overall_score=50.0,
                completeness_score=50.0,
                relevance_score=50.0,
                experience_score=50.0,
                skills_score=50.0,
                education_score=50.0,
                presentation_score=50.0,
            )
            return default_score, [], ExperienceLevelEnum.MID
    
    async def _score_completeness(self, cv_content: CVContent) -> float:
        """Score CV completeness (0-100)"""
        score = 0.0
        max_score = 100.0
        
        personal_info = cv_content.personal_info
        
        # Essential information (60 points)
        if personal_info.full_name:
            score += 15
        if personal_info.email:
            score += 15
        if personal_info.phone:
            score += 15
        if cv_content.skills:
            score += 15
        
        # Important sections (30 points)
        if cv_content.experience:
            score += 15
        if cv_content.education:
            score += 10
        if personal_info.summary:
            score += 5
        
        # Additional information (10 points)
        if personal_info.linkedin:
            score += 3
        if personal_info.location:
            score += 2
        if cv_content.languages:
            score += 2
        if cv_content.certifications:
            score += 3
        
        return min(score, max_score)
    
    async def _score_relevance(
        self,
        cv_content: CVContent,
        target_position: str,
        target_industry: str,
    ) -> float:
        """Score CV relevance to target position/industry"""
        if not target_position and not target_industry:
            return 75.0  # Default score when no target specified
        
        score = 0.0
        
        # Position relevance (50 points)
        if target_position:
            position_score = await self._calculate_position_relevance(cv_content, target_position)
            score += position_score * 0.5
        
        # Industry relevance (50 points)
        if target_industry:
            industry_score = await self._calculate_industry_relevance(cv_content, target_industry)
            score += industry_score * 0.5
        
        return min(score, 100.0)
    
    async def _score_experience(self, cv_content: CVContent) -> float:
        """Score work experience quality and progression"""
        if not cv_content.experience:
            return 20.0  # Minimum score for no experience
        
        score = 0.0
        
        # Experience quantity (30 points)
        exp_count = len(cv_content.experience)
        if exp_count >= 5:
            score += 30
        elif exp_count >= 3:
            score += 25
        elif exp_count >= 2:
            score += 20
        else:
            score += 15
        
        # Experience quality (40 points)
        total_months = sum(exp.duration_months or 0 for exp in cv_content.experience)
        if total_months >= 120:  # 10+ years
            score += 40
        elif total_months >= 60:  # 5+ years
            score += 35
        elif total_months >= 36:  # 3+ years
            score += 30
        elif total_months >= 12:  # 1+ year
            score += 25
        else:
            score += 15
        
        # Career progression (30 points)
        progression_score = await self._analyze_career_progression(cv_content.experience)
        score += progression_score * 0.3
        
        return min(score, 100.0)
    
    async def _score_skills(self, cv_content: CVContent, target_industry: str) -> float:
        """Score skills relevance and diversity"""
        if not cv_content.skills:
            return 10.0
        
        score = 0.0
        skills = cv_content.skills
        
        # Skills quantity (25 points)
        skill_count = len(skills)
        if skill_count >= 20:
            score += 25
        elif skill_count >= 15:
            score += 22
        elif skill_count >= 10:
            score += 20
        elif skill_count >= 5:
            score += 15
        else:
            score += 10
        
        # Skills diversity (25 points)
        categories = set(skill.category for skill in skills)
        diversity_score = min(len(categories) * 5, 25)
        score += diversity_score
        
        # Skills relevance to industry (50 points)
        if target_industry and target_industry in self.industry_skills:
            relevance_score = await self._calculate_skills_relevance(skills, target_industry)
            score += relevance_score * 0.5
        else:
            # Default scoring without industry context
            score += 35
        
        return min(score, 100.0)
    
    async def _score_education(self, cv_content: CVContent) -> float:
        """Score education background"""
        if not cv_content.education:
            return 30.0  # Minimum score for experience-based profiles
        
        score = 0.0
        education = cv_content.education
        
        # Education level (60 points)
        highest_degree = await self._get_highest_degree(education)
        degree_scores = {
            "phd": 60,
            "doctorate": 60,
            "master": 50,
            "bachelor": 40,
            "associate": 30,
            "diploma": 25,
            "certificate": 20,
        }
        
        for degree_type, points in degree_scores.items():
            if degree_type in highest_degree.lower():
                score += points
                break
        else:
            score += 20  # Default for unrecognized degrees
        
        # Institution quality (20 points)
        # This could be enhanced with institution rankings
        if any(edu.institution for edu in education):
            score += 15
        
        # Relevance (20 points)
        relevant_fields = ["computer science", "engineering", "business", "technology"]
        for edu in education:
            if edu.field_of_study and any(field in edu.field_of_study.lower() for field in relevant_fields):
                score += 20
                break
        else:
            score += 10
        
        return min(score, 100.0)
    
    async def _score_presentation(self, cv_content: CVContent) -> float:
        """Score CV presentation and formatting quality"""
        score = 0.0
        
        # Text quality (40 points)
        text_quality = cv_content.text_quality_score * 40
        score += text_quality
        
        # Structure and organization (30 points)
        structure_score = await self._analyze_structure_quality(cv_content)
        score += structure_score * 0.3
        
        # Language quality (30 points)
        language_score = await self._analyze_language_quality(cv_content)
        score += language_score * 0.3
        
        return min(score, 100.0)
    
    async def _determine_experience_level(self, cv_content: CVContent) -> ExperienceLevelEnum:
        """Determine experience level based on work history"""
        if not cv_content.experience:
            return ExperienceLevelEnum.ENTRY
        
        total_months = sum(exp.duration_months or 0 for exp in cv_content.experience)
        
        for level, (min_months, max_months) in self.experience_thresholds.items():
            if min_months <= total_months < max_months:
                return level
        
        return ExperienceLevelEnum.SENIOR  # Default fallback
    
    async def _generate_recommendations(
        self,
        cv_content: CVContent,
        scores: ScoreBreakdown,
        target_position: str,
        target_industry: str,
    ) -> List[Recommendation]:
        """Generate improvement recommendations"""
        recommendations = []
        
        # Completeness recommendations
        if scores.completeness_score < 80:
            completeness_recs = await self._get_completeness_recommendations(cv_content)
            recommendations.extend(completeness_recs)
        
        # Skills recommendations
        if scores.skills_score < 70:
            skills_recs = await self._get_skills_recommendations(cv_content, target_industry)
            recommendations.extend(skills_recs)
        
        # Experience recommendations
        if scores.experience_score < 70:
            experience_recs = await self._get_experience_recommendations(cv_content)
            recommendations.extend(experience_recs)
        
        # Education recommendations
        if scores.education_score < 60:
            education_recs = await self._get_education_recommendations(cv_content)
            recommendations.extend(education_recs)
        
        # Presentation recommendations
        if scores.presentation_score < 70:
            presentation_recs = await self._get_presentation_recommendations(cv_content)
            recommendations.extend(presentation_recs)
        
        # Sort by priority and impact
        recommendations.sort(key=lambda x: (x.priority, -x.impact_score))
        
        return recommendations[:10]  # Return top 10 recommendations

    async def _calculate_position_relevance(self, cv_content: CVContent, target_position: str) -> float:
        """Calculate relevance to target position"""
        score = 0.0
        target_lower = target_position.lower()

        # Check experience titles
        for exp in cv_content.experience:
            if exp.position and target_lower in exp.position.lower():
                score += 30
                break

        # Check skills relevance
        position_keywords = self._extract_position_keywords(target_position)
        skill_matches = sum(
            1 for skill in cv_content.skills
            if any(keyword in skill.name.lower() for keyword in position_keywords)
        )

        if skill_matches > 0:
            score += min(skill_matches * 10, 40)

        # Check summary relevance
        if cv_content.personal_info.summary:
            summary_matches = sum(
                1 for keyword in position_keywords
                if keyword in cv_content.personal_info.summary.lower()
            )
            score += min(summary_matches * 5, 30)

        return min(score, 100.0)

    async def _calculate_industry_relevance(self, cv_content: CVContent, target_industry: str) -> float:
        """Calculate relevance to target industry"""
        score = 0.0
        industry_lower = target_industry.lower()

        # Check company industries (simplified)
        industry_keywords = self._get_industry_keywords(target_industry)

        for exp in cv_content.experience:
            if exp.company:
                company_matches = sum(
                    1 for keyword in industry_keywords
                    if keyword in exp.company.lower()
                )
                if company_matches > 0:
                    score += 25
                    break

        # Check skills relevance to industry
        if target_industry in self.industry_skills:
            industry_skill_weights = self.industry_skills[target_industry]
            weighted_score = 0.0

            for skill in cv_content.skills:
                category_weight = industry_skill_weights.get(skill.category, 0.1)
                weighted_score += skill.confidence * category_weight

            score += min(weighted_score * 50, 75)

        return min(score, 100.0)

    async def _analyze_career_progression(self, experiences: List[Experience]) -> float:
        """Analyze career progression quality"""
        if len(experiences) < 2:
            return 50.0  # Neutral score for single job

        score = 0.0

        # Sort by start date (most recent first)
        sorted_exp = sorted(
            experiences,
            key=lambda x: x.start_date or "0000",
            reverse=True
        )

        # Check for title progression
        titles = [exp.position.lower() for exp in sorted_exp if exp.position]
        progression_indicators = ["senior", "lead", "manager", "director", "vp", "ceo"]

        progression_score = 0
        for i, title in enumerate(titles[:-1]):
            next_title = titles[i + 1]

            # Check if current title has higher level indicators
            current_level = sum(1 for indicator in progression_indicators if indicator in title)
            next_level = sum(1 for indicator in progression_indicators if indicator in next_title)

            if current_level > next_level:
                progression_score += 20

        score += min(progression_score, 60)

        # Check tenure consistency
        tenures = [exp.duration_months or 0 for exp in experiences]
        avg_tenure = sum(tenures) / len(tenures) if tenures else 0

        if 12 <= avg_tenure <= 48:  # 1-4 years average
            score += 40
        elif 6 <= avg_tenure <= 60:  # 6 months - 5 years
            score += 30
        else:
            score += 20

        return min(score, 100.0)

    async def _calculate_skills_relevance(self, skills: List[Skill], target_industry: str) -> float:
        """Calculate skills relevance to target industry"""
        if target_industry not in self.industry_skills:
            return 70.0  # Default score

        industry_weights = self.industry_skills[target_industry]
        weighted_score = 0.0
        total_weight = 0.0

        for skill in skills:
            category_weight = industry_weights.get(skill.category, 0.1)
            weighted_score += skill.confidence * category_weight
            total_weight += category_weight

        if total_weight > 0:
            normalized_score = (weighted_score / total_weight) * 100
            return min(normalized_score, 100.0)

        return 50.0

    async def _get_highest_degree(self, education: List[Education]) -> str:
        """Get the highest degree from education list"""
        degree_hierarchy = {
            "phd": 6, "doctorate": 6, "ph.d": 6,
            "master": 5, "msc": 5, "mba": 5, "ma": 5,
            "bachelor": 4, "bsc": 4, "ba": 4, "bs": 4,
            "associate": 3,
            "diploma": 2,
            "certificate": 1,
        }

        highest_level = 0
        highest_degree = ""

        for edu in education:
            if edu.degree:
                degree_lower = edu.degree.lower()
                for degree_type, level in degree_hierarchy.items():
                    if degree_type in degree_lower and level > highest_level:
                        highest_level = level
                        highest_degree = edu.degree

        return highest_degree or "unknown"

    async def _analyze_structure_quality(self, cv_content: CVContent) -> float:
        """Analyze CV structure quality"""
        score = 0.0

        # Check for logical section order
        sections_present = []
        if cv_content.personal_info.summary:
            sections_present.append("summary")
        if cv_content.experience:
            sections_present.append("experience")
        if cv_content.skills:
            sections_present.append("skills")
        if cv_content.education:
            sections_present.append("education")

        # Ideal order bonus
        ideal_order = ["summary", "experience", "skills", "education"]
        order_score = 0
        for i, section in enumerate(sections_present):
            if i < len(ideal_order) and section == ideal_order[i]:
                order_score += 25

        score += min(order_score, 75)

        # Completeness bonus
        if len(sections_present) >= 3:
            score += 25

        return min(score, 100.0)

    async def _analyze_language_quality(self, cv_content: CVContent) -> float:
        """Analyze language quality"""
        # This is a simplified implementation
        # In production, you might use more sophisticated NLP analysis

        score = 70.0  # Base score

        # Check for summary quality
        if cv_content.personal_info.summary:
            summary = cv_content.personal_info.summary

            # Length check
            if 50 <= len(summary) <= 300:
                score += 15
            elif len(summary) > 300:
                score += 10

            # Basic grammar indicators
            if summary.count('.') >= 2:  # Multiple sentences
                score += 10

            if not re.search(r'\b(i am|i have|i can)\b', summary.lower()):
                score += 5  # Bonus for not using first person

        return min(score, 100.0)

    def _extract_position_keywords(self, position: str) -> List[str]:
        """Extract keywords from position title"""
        # Simple keyword extraction - can be enhanced with NLP
        common_words = {"and", "or", "the", "a", "an", "in", "at", "for", "with", "by"}
        words = [word.lower().strip() for word in re.split(r'[,\s]+', position)]
        return [word for word in words if len(word) > 2 and word not in common_words]

    def _get_industry_keywords(self, industry: str) -> List[str]:
        """Get keywords associated with an industry"""
        industry_keywords = {
            "technology": ["tech", "software", "digital", "it", "computer", "data"],
            "finance": ["bank", "financial", "investment", "capital", "fund"],
            "healthcare": ["health", "medical", "hospital", "clinic", "pharma"],
            "marketing": ["marketing", "advertising", "brand", "digital", "social"],
            "education": ["education", "school", "university", "academic", "learning"],
        }

        return industry_keywords.get(industry.lower(), [industry.lower()])

    async def _get_completeness_recommendations(self, cv_content: CVContent) -> List[Recommendation]:
        """Generate completeness improvement recommendations"""
        recommendations = []
        personal_info = cv_content.personal_info

        if not personal_info.email:
            recommendations.append(Recommendation(
                category="contact_info",
                priority="high",
                title="Add Email Address",
                description="Include a professional email address to enable recruiters to contact you easily.",
                impact_score=9.0
            ))

        if not personal_info.phone:
            recommendations.append(Recommendation(
                category="contact_info",
                priority="high",
                title="Add Phone Number",
                description="Include your phone number for direct contact opportunities.",
                impact_score=8.0
            ))

        if not personal_info.summary:
            recommendations.append(Recommendation(
                category="summary",
                priority="medium",
                title="Add Professional Summary",
                description="Write a compelling 2-3 sentence summary highlighting your key strengths and career goals.",
                impact_score=7.5
            ))

        if not personal_info.linkedin:
            recommendations.append(Recommendation(
                category="social_media",
                priority="medium",
                title="Add LinkedIn Profile",
                description="Include your LinkedIn profile URL to showcase your professional network and endorsements.",
                impact_score=6.0
            ))

        if len(cv_content.skills) < 10:
            recommendations.append(Recommendation(
                category="skills",
                priority="medium",
                title="Expand Skills Section",
                description="Add more relevant skills to demonstrate your capabilities. Aim for 10-15 key skills.",
                impact_score=7.0
            ))

        return recommendations

    async def _get_skills_recommendations(self, cv_content: CVContent, target_industry: str) -> List[Recommendation]:
        """Generate skills improvement recommendations"""
        recommendations = []

        if len(cv_content.skills) < 5:
            recommendations.append(Recommendation(
                category="skills",
                priority="high",
                title="Add More Skills",
                description="Include more technical and soft skills relevant to your field. Minimum 5-10 skills recommended.",
                impact_score=8.5
            ))

        # Check skill categories diversity
        categories = set(skill.category for skill in cv_content.skills)
        if len(categories) < 3:
            recommendations.append(Recommendation(
                category="skills",
                priority="medium",
                title="Diversify Skill Categories",
                description="Include a mix of technical skills, soft skills, and tools to show well-rounded capabilities.",
                impact_score=6.5
            ))

        # Industry-specific recommendations
        if target_industry == "technology":
            tech_skills = [skill for skill in cv_content.skills if skill.category == "technical"]
            if len(tech_skills) < 5:
                recommendations.append(Recommendation(
                    category="skills",
                    priority="high",
                    title="Add More Technical Skills",
                    description="For technology roles, include programming languages, frameworks, and technical tools you've used.",
                    impact_score=9.0
                ))

        return recommendations

    async def _get_experience_recommendations(self, cv_content: CVContent) -> List[Recommendation]:
        """Generate experience improvement recommendations"""
        recommendations = []

        if not cv_content.experience:
            recommendations.append(Recommendation(
                category="experience",
                priority="high",
                title="Add Work Experience",
                description="Include your work history with job titles, companies, dates, and key achievements.",
                impact_score=9.5
            ))
            return recommendations

        # Check for missing descriptions
        missing_descriptions = [exp for exp in cv_content.experience if not exp.description]
        if missing_descriptions:
            recommendations.append(Recommendation(
                category="experience",
                priority="medium",
                title="Add Job Descriptions",
                description="Include detailed descriptions of your responsibilities and achievements for each role.",
                impact_score=7.0
            ))

        # Check for quantifiable achievements
        has_numbers = any(
            re.search(r'\d+', exp.description or '')
            for exp in cv_content.experience
        )
        if not has_numbers:
            recommendations.append(Recommendation(
                category="experience",
                priority="medium",
                title="Add Quantifiable Achievements",
                description="Include specific numbers, percentages, or metrics to demonstrate your impact (e.g., 'Increased sales by 25%').",
                impact_score=8.0
            ))

        return recommendations

    async def _get_education_recommendations(self, cv_content: CVContent) -> List[Recommendation]:
        """Generate education improvement recommendations"""
        recommendations = []

        if not cv_content.education:
            recommendations.append(Recommendation(
                category="education",
                priority="low",
                title="Add Education Background",
                description="Include your educational qualifications, even if you're primarily experience-focused.",
                impact_score=5.0
            ))
            return recommendations

        # Check for missing fields of study
        missing_fields = [edu for edu in cv_content.education if not edu.field_of_study]
        if missing_fields:
            recommendations.append(Recommendation(
                category="education",
                priority="low",
                title="Add Field of Study",
                description="Specify your major or field of study for each educational qualification.",
                impact_score=4.0
            ))

        return recommendations

    async def _get_presentation_recommendations(self, cv_content: CVContent) -> List[Recommendation]:
        """Generate presentation improvement recommendations"""
        recommendations = []

        if cv_content.text_quality_score < 0.7:
            recommendations.append(Recommendation(
                category="formatting",
                priority="medium",
                title="Improve Document Quality",
                description="Ensure your CV is well-formatted, properly structured, and free of formatting issues.",
                impact_score=6.5
            ))

        # Check summary length
        if cv_content.personal_info.summary:
            summary_length = len(cv_content.personal_info.summary)
            if summary_length < 50:
                recommendations.append(Recommendation(
                    category="summary",
                    priority="medium",
                    title="Expand Professional Summary",
                    description="Your summary is too brief. Aim for 2-3 sentences that highlight your key strengths.",
                    impact_score=6.0
                ))
            elif summary_length > 300:
                recommendations.append(Recommendation(
                    category="summary",
                    priority="medium",
                    title="Shorten Professional Summary",
                    description="Your summary is too long. Keep it concise and impactful - 2-3 sentences maximum.",
                    impact_score=5.5
                ))

        return recommendations
