"""
Multilingual NLP processor for Hebrew and English CV analysis
"""

import spacy
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import structlog
from hebrew_tokenizer import Tokenizer as HebrewTokenizer

from app.core.config import settings
from app.core.exceptions import NLPProcessingError
from app.core.logging import C<PERSON><PERSON>ysisLogger
from app.models.cv_analysis import PersonalInfo, Skill, Experience, Education, Language, Certification

logger = CVAnalysisLogger("nlp_processor")


class MultilingualNLPProcessor:
    """Advanced NLP processor with Hebrew and English support"""
    
    def __init__(self):
        self.nlp_en = None
        self.nlp_he = None
        self.hebrew_tokenizer = HebrewTokenizer()
        
        # Skill patterns for both languages
        self.skill_patterns = {
            "en": self._load_english_skill_patterns(),
            "he": self._load_hebrew_skill_patterns(),
        }
        
        # Entity patterns
        self.entity_patterns = {
            "email": re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            "phone": re.compile(r'(?:\+972|0)(?:[-\s])?(?:[2-9]\d{1,2})(?:[-\s])?\d{3}(?:[-\s])?\d{4}'),
            "linkedin": re.compile(r'(?:linkedin\.com/in/|linkedin\.com/profile/view\?id=)([A-Za-z0-9\-]+)'),
            "github": re.compile(r'(?:github\.com/)([A-Za-z0-9\-]+)'),
        }
        
        # Date patterns
        self.date_patterns = [
            re.compile(r'(\d{1,2})/(\d{1,2})/(\d{4})'),  # MM/DD/YYYY
            re.compile(r'(\d{4})-(\d{1,2})-(\d{1,2})'),  # YYYY-MM-DD
            re.compile(r'(\d{1,2})/(\d{4})'),  # MM/YYYY
            re.compile(r'(\d{4})'),  # YYYY
        ]
    
    async def initialize(self):
        """Initialize NLP models"""
        try:
            # Load English model
            self.nlp_en = spacy.load(settings.SPACY_MODEL_EN)
            logger.logger.info("english_nlp_model_loaded", model=settings.SPACY_MODEL_EN)
            
            # Try to load Hebrew model
            try:
                self.nlp_he = spacy.load(settings.SPACY_MODEL_HE)
                logger.logger.info("hebrew_nlp_model_loaded", model=settings.SPACY_MODEL_HE)
            except OSError:
                logger.logger.warning("hebrew_nlp_model_not_found", model=settings.SPACY_MODEL_HE)
                # Use English model as fallback for Hebrew
                self.nlp_he = self.nlp_en
            
        except Exception as e:
            raise NLPProcessingError(f"Failed to initialize NLP models: {str(e)}")
    
    async def process_cv_text(self, text: str, language: str) -> Dict[str, Any]:
        """
        Process CV text and extract structured information
        
        Args:
            text: Cleaned CV text
            language: Primary language (en/he)
            
        Returns:
            Dictionary with extracted information
        """
        try:
            # Choose appropriate NLP model
            nlp = self.nlp_en if language == "en" else self.nlp_he
            
            # Process text
            doc = nlp(text)
            
            # Extract different components
            personal_info = await self._extract_personal_info(text, doc, language)
            skills = await self._extract_skills(text, doc, language)
            experience = await self._extract_experience(text, doc, language)
            education = await self._extract_education(text, doc, language)
            languages = await self._extract_languages(text, doc, language)
            certifications = await self._extract_certifications(text, doc, language)
            
            result = {
                "personal_info": personal_info,
                "skills": skills,
                "experience": experience,
                "education": education,
                "languages": languages,
                "certifications": certifications,
                "entities_found": len(doc.ents),
                "processing_language": language,
            }
            
            logger.log_nlp_processing(
                language=language,
                entities_found=len(doc.ents),
                skills_extracted=len(skills),
            )
            
            return result
            
        except Exception as e:
            logger.log_error(
                error_type="nlp_processing_failed",
                error_message=str(e),
                context={"language": language, "text_length": len(text)}
            )
            raise NLPProcessingError(f"NLP processing failed: {str(e)}")
    
    async def _extract_personal_info(self, text: str, doc, language: str) -> PersonalInfo:
        """Extract personal information"""
        info = PersonalInfo()
        
        # Extract email
        email_match = self.entity_patterns["email"].search(text)
        if email_match:
            info.email = email_match.group()
        
        # Extract phone
        phone_match = self.entity_patterns["phone"].search(text)
        if phone_match:
            info.phone = phone_match.group()
        
        # Extract LinkedIn
        linkedin_match = self.entity_patterns["linkedin"].search(text)
        if linkedin_match:
            info.linkedin = linkedin_match.group()
        
        # Extract GitHub
        github_match = self.entity_patterns["github"].search(text)
        if github_match:
            info.github = github_match.group()
        
        # Extract name using NER
        for ent in doc.ents:
            if ent.label_ == "PERSON" and not info.full_name:
                # Take the first person entity as the name
                info.full_name = ent.text.strip()
                break
        
        # Extract location
        locations = [ent.text for ent in doc.ents if ent.label_ in ["GPE", "LOC"]]
        if locations:
            info.location = locations[0]
        
        # Extract summary (first paragraph or section)
        lines = text.split('\n')
        for i, line in enumerate(lines[:10]):  # Check first 10 lines
            if len(line.strip()) > 100 and not any(pattern in line.lower() for pattern in ['email', 'phone', 'address']):
                info.summary = line.strip()
                break
        
        return info
    
    async def _extract_skills(self, text: str, doc, language: str) -> List[Skill]:
        """Extract skills with categorization"""
        skills = []
        skill_patterns = self.skill_patterns.get(language, self.skill_patterns["en"])
        
        # Extract skills using patterns
        for category, patterns in skill_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    skill_name = match.group().strip()
                    if len(skill_name) > 2:  # Filter out very short matches
                        skills.append(Skill(
                            name=skill_name,
                            category=category,
                            confidence=0.8,  # Pattern-based confidence
                            context=self._get_context(text, match.start(), match.end())
                        ))
        
        # Extract skills using NER
        for ent in doc.ents:
            if ent.label_ in ["ORG", "PRODUCT"] and len(ent.text) > 2:
                # Check if it's a known technology/skill
                if self._is_technology_skill(ent.text):
                    skills.append(Skill(
                        name=ent.text,
                        category="technical",
                        confidence=0.7,  # NER-based confidence
                        context=self._get_context(text, ent.start_char, ent.end_char)
                    ))
        
        # Remove duplicates and return top skills
        unique_skills = {}
        for skill in skills:
            key = skill.name.lower()
            if key not in unique_skills or skill.confidence > unique_skills[key].confidence:
                unique_skills[key] = skill
        
        return list(unique_skills.values())[:50]  # Limit to top 50 skills
    
    async def _extract_experience(self, text: str, doc, language: str) -> List[Experience]:
        """Extract work experience"""
        experiences = []
        
        # Define section patterns
        if language == "he":
            section_patterns = [
                r'(?i)(ניסיון מקצועי|ניסיון עבודה|עבודה)',
                r'(?i)(תפקידים קודמים|עבודות קודמות)',
            ]
        else:
            section_patterns = [
                r'(?i)(work experience|professional experience|employment)',
                r'(?i)(career history|work history)',
            ]
        
        # Find experience section
        experience_section = self._find_section(text, section_patterns)
        if not experience_section:
            return experiences
        
        # Parse experience entries
        entries = self._parse_experience_entries(experience_section, language)
        
        for entry in entries:
            exp = Experience(
                company=entry.get("company"),
                position=entry.get("position", ""),
                start_date=entry.get("start_date"),
                end_date=entry.get("end_date"),
                duration_months=entry.get("duration_months"),
                description=entry.get("description"),
                skills_used=entry.get("skills", []),
                achievements=entry.get("achievements", []),
                is_current=entry.get("is_current", False),
            )
            experiences.append(exp)
        
        return experiences
    
    async def _extract_education(self, text: str, doc, language: str) -> List[Education]:
        """Extract education information"""
        education_list = []
        
        # Define section patterns
        if language == "he":
            section_patterns = [
                r'(?i)(השכלה|לימודים|תואר)',
                r'(?i)(השכלה אקדמית|רקע אקדמי)',
            ]
        else:
            section_patterns = [
                r'(?i)(education|academic background)',
                r'(?i)(qualifications|degrees)',
            ]
        
        # Find education section
        education_section = self._find_section(text, section_patterns)
        if not education_section:
            return education_list
        
        # Parse education entries
        entries = self._parse_education_entries(education_section, language)
        
        for entry in entries:
            edu = Education(
                institution=entry.get("institution"),
                degree=entry.get("degree"),
                field_of_study=entry.get("field"),
                start_date=entry.get("start_date"),
                end_date=entry.get("end_date"),
                gpa=entry.get("gpa"),
                achievements=entry.get("achievements", []),
            )
            education_list.append(edu)
        
        return education_list
    
    async def _extract_languages(self, text: str, doc, language: str) -> List[Language]:
        """Extract language proficiencies"""
        languages = []
        
        # Language patterns
        if language == "he":
            lang_patterns = {
                r'(?i)(עברית|hebrew)': "Hebrew",
                r'(?i)(אנגלית|english)': "English",
                r'(?i)(ערבית|arabic)': "Arabic",
                r'(?i)(צרפתית|french)': "French",
                r'(?i)(גרמנית|german)': "German",
                r'(?i)(ספרדית|spanish)': "Spanish",
                r'(?i)(רוסית|russian)': "Russian",
            }
        else:
            lang_patterns = {
                r'(?i)hebrew': "Hebrew",
                r'(?i)english': "English",
                r'(?i)arabic': "Arabic",
                r'(?i)french': "French",
                r'(?i)german': "German",
                r'(?i)spanish': "Spanish",
                r'(?i)russian': "Russian",
            }
        
        # Proficiency patterns
        proficiency_patterns = {
            r'(?i)(native|mother tongue|שפת אם)': "Native",
            r'(?i)(fluent|רהוט)': "Fluent",
            r'(?i)(advanced|מתקדם)': "Advanced",
            r'(?i)(intermediate|בינוני)': "Intermediate",
            r'(?i)(basic|בסיסי)': "Basic",
        }
        
        for pattern, lang_name in lang_patterns.items():
            matches = re.finditer(pattern, text)
            for match in matches:
                # Look for proficiency level near the language mention
                context = self._get_context(text, match.start(), match.end(), window=50)
                proficiency = "Intermediate"  # Default
                confidence = 0.6
                
                for prof_pattern, prof_level in proficiency_patterns.items():
                    if re.search(prof_pattern, context):
                        proficiency = prof_level
                        confidence = 0.8
                        break
                
                languages.append(Language(
                    language=lang_name,
                    proficiency=proficiency,
                    confidence=confidence,
                ))
        
        return languages
    
    async def _extract_certifications(self, text: str, doc, language: str) -> List[Certification]:
        """Extract professional certifications"""
        certifications = []
        
        # Certification patterns
        cert_patterns = [
            r'(?i)(certified|certification|certificate)',
            r'(?i)(license|licensed)',
            r'(?i)(AWS|Azure|Google Cloud|GCP)',
            r'(?i)(PMP|CISSP|CISA|CISM)',
            r'(?i)(Microsoft|Oracle|Cisco)',
        ]
        
        if language == "he":
            cert_patterns.extend([
                r'(?i)(תעודה|הסמכה|רישיון)',
                r'(?i)(קורס|הכשרה)',
            ])
        
        for pattern in cert_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                context = self._get_context(text, match.start(), match.end(), window=100)
                
                # Extract certification name from context
                cert_name = self._extract_certification_name(context)
                if cert_name:
                    certifications.append(Certification(
                        name=cert_name,
                        issuer=self._extract_issuer(context),
                        issue_date=self._extract_date(context),
                    ))
        
        return certifications
    
    def _load_english_skill_patterns(self) -> Dict[str, List[str]]:
        """Load English skill patterns"""
        return {
            "technical": [
                r'\b(?:Python|Java|JavaScript|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin)\b',
                r'\b(?:React|Angular|Vue|Node\.js|Django|Flask|Spring|Laravel)\b',
                r'\b(?:AWS|Azure|GCP|Docker|Kubernetes|Jenkins|Git|Linux)\b',
                r'\b(?:SQL|MySQL|PostgreSQL|MongoDB|Redis|Elasticsearch)\b',
                r'\b(?:Machine Learning|AI|Data Science|Deep Learning|NLP)\b',
            ],
            "soft": [
                r'\b(?:Leadership|Communication|Teamwork|Problem Solving)\b',
                r'\b(?:Project Management|Time Management|Critical Thinking)\b',
                r'\b(?:Creativity|Adaptability|Collaboration|Innovation)\b',
            ],
            "tool": [
                r'\b(?:Excel|PowerPoint|Word|Photoshop|Illustrator|Figma)\b',
                r'\b(?:Jira|Confluence|Slack|Trello|Asana|Notion)\b',
                r'\b(?:Tableau|Power BI|Looker|Grafana|Kibana)\b',
            ],
        }
    
    def _load_hebrew_skill_patterns(self) -> Dict[str, List[str]]:
        """Load Hebrew skill patterns"""
        return {
            "technical": [
                r'\b(?:פייתון|ג\'אווה|ג\'אווהסקריפט|סי פלוס פלוס|סי שארפ)\b',
                r'\b(?:ריאקט|אנגולר|נוד|ג\'נגו|פלאסק)\b',
                r'\b(?:AWS|Azure|דוקר|קוברנטס|גיט|לינוקס)\b',
                r'\b(?:SQL|מסד נתונים|בסיס נתונים)\b',
                r'\b(?:למידת מכונה|בינה מלאכותית|מדעי נתונים)\b',
            ],
            "soft": [
                r'\b(?:מנהיגות|תקשורת|עבודת צוות|פתרון בעיות)\b',
                r'\b(?:ניהול פרויקטים|ניהול זמן|חשיבה ביקורתית)\b',
                r'\b(?:יצירתיות|הסתגלות|שיתוף פעולה|חדשנות)\b',
            ],
            "tool": [
                r'\b(?:אקסל|פאוורפוינט|וורד|פוטושופ|איליסטרייטור)\b',
                r'\b(?:ג\'ירה|קונפלואנס|סלאק|טרלו|אסאנה)\b',
            ],
        }
    
    def _get_context(self, text: str, start: int, end: int, window: int = 30) -> str:
        """Get context around a match"""
        context_start = max(0, start - window)
        context_end = min(len(text), end + window)
        return text[context_start:context_end]
    
    def _is_technology_skill(self, text: str) -> bool:
        """Check if text represents a technology skill"""
        tech_keywords = [
            "microsoft", "google", "amazon", "oracle", "ibm", "apple",
            "framework", "library", "platform", "tool", "software",
            "technology", "system", "database", "server", "cloud",
        ]
        return any(keyword in text.lower() for keyword in tech_keywords)
    
    def _find_section(self, text: str, patterns: List[str]) -> Optional[str]:
        """Find a specific section in the text"""
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                # Extract section content (next 500 characters or until next section)
                start = match.end()
                end = min(start + 1000, len(text))
                return text[start:end]
        return None
    
    def _parse_experience_entries(self, section: str, language: str) -> List[Dict[str, Any]]:
        """Parse experience entries from section text"""
        # This is a simplified implementation
        # In production, you'd want more sophisticated parsing
        entries = []
        lines = section.split('\n')
        
        current_entry = {}
        for line in lines:
            line = line.strip()
            if not line:
                if current_entry:
                    entries.append(current_entry)
                    current_entry = {}
                continue
            
            # Simple heuristics for parsing
            if not current_entry.get("position"):
                current_entry["position"] = line
            elif not current_entry.get("company"):
                current_entry["company"] = line
            else:
                current_entry.setdefault("description", "")
                current_entry["description"] += " " + line
        
        if current_entry:
            entries.append(current_entry)
        
        return entries
    
    def _parse_education_entries(self, section: str, language: str) -> List[Dict[str, Any]]:
        """Parse education entries from section text"""
        # Simplified implementation
        entries = []
        lines = section.split('\n')
        
        current_entry = {}
        for line in lines:
            line = line.strip()
            if not line:
                if current_entry:
                    entries.append(current_entry)
                    current_entry = {}
                continue
            
            if not current_entry.get("degree"):
                current_entry["degree"] = line
            elif not current_entry.get("institution"):
                current_entry["institution"] = line
        
        if current_entry:
            entries.append(current_entry)
        
        return entries
    
    def _extract_certification_name(self, context: str) -> Optional[str]:
        """Extract certification name from context"""
        # Simplified implementation
        words = context.split()
        if len(words) > 0:
            return " ".join(words[:3])  # Take first 3 words as cert name
        return None
    
    def _extract_issuer(self, context: str) -> Optional[str]:
        """Extract certification issuer"""
        issuers = ["Microsoft", "Amazon", "Google", "Oracle", "Cisco", "IBM"]
        for issuer in issuers:
            if issuer.lower() in context.lower():
                return issuer
        return None
    
    def _extract_date(self, context: str) -> Optional[str]:
        """Extract date from context"""
        for pattern in self.date_patterns:
            match = pattern.search(context)
            if match:
                return match.group()
        return None
