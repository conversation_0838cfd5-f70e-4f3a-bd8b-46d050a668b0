"""
Data models for CV analysis
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Optional, Any, Union
from datetime import datetime
from enum import Enum
import uuid


class LanguageEnum(str, Enum):
    """Supported languages"""
    HEBREW = "he"
    ENGLISH = "en"
    AUTO = "auto"


class AnalysisStatusEnum(str, Enum):
    """Analysis status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class SkillCategoryEnum(str, Enum):
    """Skill categories"""
    TECHNICAL = "technical"
    SOFT = "soft"
    LANGUAGE = "language"
    CERTIFICATION = "certification"
    TOOL = "tool"
    FRAMEWORK = "framework"
    OTHER = "other"


class ExperienceLevelEnum(str, Enum):
    """Experience levels"""
    ENTRY = "entry"
    JUNIOR = "junior"
    MID = "mid"
    SENIOR = "senior"
    LEAD = "lead"
    EXECUTIVE = "executive"


class CVFileInfo(BaseModel):
    """CV file information"""
    filename: str
    size: int
    content_type: str
    upload_timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    @validator('size')
    def validate_size(cls, v):
        if v > 4 * 1024 * 1024:  # 4MB
            raise ValueError('File size exceeds 4MB limit')
        return v


class PersonalInfo(BaseModel):
    """Personal information extracted from CV"""
    full_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None
    linkedin: Optional[str] = None
    github: Optional[str] = None
    website: Optional[str] = None
    summary: Optional[str] = None


class Skill(BaseModel):
    """Individual skill"""
    name: str
    category: SkillCategoryEnum
    confidence: float = Field(ge=0.0, le=1.0)
    years_experience: Optional[int] = None
    proficiency_level: Optional[str] = None
    context: Optional[str] = None  # Where it was mentioned


class Experience(BaseModel):
    """Work experience entry"""
    company: Optional[str] = None
    position: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    duration_months: Optional[int] = None
    description: Optional[str] = None
    skills_used: List[str] = []
    achievements: List[str] = []
    is_current: bool = False


class Education(BaseModel):
    """Education entry"""
    institution: Optional[str] = None
    degree: Optional[str] = None
    field_of_study: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    gpa: Optional[float] = None
    achievements: List[str] = []


class Language(BaseModel):
    """Language proficiency"""
    language: str
    proficiency: str  # Native, Fluent, Intermediate, Basic
    confidence: float = Field(ge=0.0, le=1.0)


class Certification(BaseModel):
    """Professional certification"""
    name: str
    issuer: Optional[str] = None
    issue_date: Optional[str] = None
    expiry_date: Optional[str] = None
    credential_id: Optional[str] = None


class CVContent(BaseModel):
    """Extracted CV content"""
    raw_text: str
    personal_info: PersonalInfo
    skills: List[Skill] = []
    experience: List[Experience] = []
    education: List[Education] = []
    languages: List[Language] = []
    certifications: List[Certification] = []
    detected_language: LanguageEnum
    text_quality_score: float = Field(ge=0.0, le=1.0)


class ScoreBreakdown(BaseModel):
    """Detailed score breakdown"""
    overall_score: float = Field(ge=0.0, le=100.0)
    completeness_score: float = Field(ge=0.0, le=100.0)
    relevance_score: float = Field(ge=0.0, le=100.0)
    experience_score: float = Field(ge=0.0, le=100.0)
    skills_score: float = Field(ge=0.0, le=100.0)
    education_score: float = Field(ge=0.0, le=100.0)
    presentation_score: float = Field(ge=0.0, le=100.0)


class Recommendation(BaseModel):
    """Improvement recommendation"""
    category: str
    priority: str  # high, medium, low
    title: str
    description: str
    impact_score: float = Field(ge=0.0, le=10.0)


class CVAnalysisResult(BaseModel):
    """Complete CV analysis result"""
    analysis_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    file_info: CVFileInfo
    content: CVContent
    score_breakdown: ScoreBreakdown
    recommendations: List[Recommendation] = []
    experience_level: ExperienceLevelEnum
    industry_match: List[str] = []
    keyword_density: Dict[str, float] = {}
    analysis_timestamp: datetime = Field(default_factory=datetime.utcnow)
    processing_time: float  # seconds
    status: AnalysisStatusEnum = AnalysisStatusEnum.COMPLETED


class AnalysisRequest(BaseModel):
    """CV analysis request"""
    language: LanguageEnum = LanguageEnum.AUTO
    target_position: Optional[str] = None
    target_industry: Optional[str] = None
    include_recommendations: bool = True
    detailed_analysis: bool = True


class AnalysisResponse(BaseModel):
    """CV analysis response"""
    analysis_id: str
    status: AnalysisStatusEnum
    message: str
    result: Optional[CVAnalysisResult] = None
    error_details: Optional[Dict[str, Any]] = None


class FeedbackRequest(BaseModel):
    """Feedback for learning system"""
    analysis_id: str
    feedback_type: str  # accuracy, relevance, completeness
    rating: int = Field(ge=1, le=5)
    comments: Optional[str] = None
    corrections: Optional[Dict[str, Any]] = None
    user_id: Optional[str] = None


class LearningMetrics(BaseModel):
    """Learning system metrics"""
    total_analyses: int
    feedback_count: int
    average_accuracy: float
    model_version: str
    last_training_date: datetime
    categories_learned: int
    skills_learned: int


class FeedbackData(BaseModel):
    """User feedback data for learning"""
    cv_id: str
    feedback_type: str  # "accuracy", "relevance", "completeness"
    rating: int = Field(..., ge=1, le=5)
    comments: Optional[str] = None
    corrections: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class AnalysisStatus(BaseModel):
    """CV analysis service status"""
    service_status: str
    nlp_models_loaded: bool
    ml_models_loaded: bool
    learning_system_active: bool
    supported_languages: List[str]
    supported_file_types: List[str]
    max_file_size_mb: float
    version: str


class LearningInsights(BaseModel):
    """Learning system insights and metrics"""
    total_cvs_processed: int
    feedback_received: int
    new_categories_discovered: int
    models_updated: int
    accuracy_improvements: List[Dict[str, Any]]
    learning_velocity: Dict[str, Any]
    recent_improvements: List[Dict[str, Any]]
    system_recommendations: List[Dict[str, Any]]


class CVAnalysisRequest(BaseModel):
    """CV analysis request model"""
    target_position: Optional[str] = None
    target_industry: Optional[str] = None
    language: str = "auto"
