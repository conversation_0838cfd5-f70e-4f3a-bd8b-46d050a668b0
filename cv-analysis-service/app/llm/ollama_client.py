"""
Ollama LLM Client for CV Analysis
Provides integration with local Ollama instance for advanced CV content analysis
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
import httpx
from pydantic import BaseModel, Field
import structlog

from app.core.config import get_settings

logger = structlog.get_logger(__name__)


class LLMAnalysisRequest(BaseModel):
    """Request model for LLM analysis"""
    text: str = Field(..., description="Text to analyze")
    analysis_type: str = Field(..., description="Type of analysis to perform")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Additional context")
    language: str = Field(default="auto", description="Text language")


class LLMAnalysisResponse(BaseModel):
    """Response model for LLM analysis"""
    analysis_type: str
    insights: Dict[str, Any]
    confidence: float = Field(ge=0.0, le=1.0)
    processing_time: float
    model_used: str


class OllamaClient:
    """Client for interacting with Ollama LLM service"""
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = self.settings.OLLAMA_BASE_URL
        self.model_name = "llama3.2:1b"  # LLaMA 3.2 1B model (lighter, faster)
        self.timeout = 120.0  # 2 minutes timeout for LLM requests
        self.max_retries = 3
        
    async def ensure_model_available(self) -> bool:
        """Ensure the required model is available in Ollama"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Check if model is available
                response = await client.get(f"{self.base_url}/api/tags")
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    model_names = [model.get("name", "") for model in models]
                    
                    if self.model_name in model_names:
                        logger.info("Model already available", model=self.model_name)
                        return True
                    
                    # Pull the model if not available
                    logger.info("Pulling model", model=self.model_name)
                    pull_response = await client.post(
                        f"{self.base_url}/api/pull",
                        json={"name": self.model_name},
                        timeout=600.0  # 10 minutes for model download
                    )
                    
                    if pull_response.status_code == 200:
                        logger.info("Model pulled successfully", model=self.model_name)
                        return True
                    else:
                        logger.error("Failed to pull model", 
                                   model=self.model_name, 
                                   status=pull_response.status_code)
                        return False
                else:
                    logger.error("Failed to connect to Ollama", status=response.status_code)
                    return False
                    
        except Exception as e:
            logger.error("Error ensuring model availability", error=str(e))
            return False
    
    async def analyze_cv_content(self, request: LLMAnalysisRequest) -> LLMAnalysisResponse:
        """Analyze CV content using LLM"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Ensure model is available
            if not await self.ensure_model_available():
                raise Exception("Required LLM model not available")
            
            # Prepare the prompt based on analysis type
            prompt = self._prepare_prompt(request)
            
            # Make request to Ollama
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json={
                        "model": self.model_name,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.3,  # Lower temperature for more consistent analysis
                            "top_p": 0.9,
                            "top_k": 40,
                            "num_predict": 1000  # Max tokens to generate
                        }
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    analysis_result = self._parse_llm_response(
                        result.get("response", ""), 
                        request.analysis_type
                    )
                    
                    processing_time = asyncio.get_event_loop().time() - start_time
                    
                    return LLMAnalysisResponse(
                        analysis_type=request.analysis_type,
                        insights=analysis_result,
                        confidence=0.85,  # Default confidence, can be improved with response analysis
                        processing_time=processing_time,
                        model_used=self.model_name
                    )
                else:
                    raise Exception(f"LLM request failed with status {response.status_code}")
                    
        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            logger.error("LLM analysis failed", error=str(e), analysis_type=request.analysis_type)
            
            # Return fallback response
            return LLMAnalysisResponse(
                analysis_type=request.analysis_type,
                insights={"error": str(e), "fallback": True},
                confidence=0.0,
                processing_time=processing_time,
                model_used=self.model_name
            )
    
    def _prepare_prompt(self, request: LLMAnalysisRequest) -> str:
        """Prepare prompt for different analysis types"""
        base_context = f"""
You are an expert HR analyst specializing in CV/resume analysis. 
Analyze the following CV content and provide insights in JSON format.

Language: {request.language}
Analysis Type: {request.analysis_type}

CV Content:
{request.text[:4000]}  # Limit text to avoid token limits
"""
        
        if request.analysis_type == "skills_extraction":
            return base_context + """

Extract and categorize all skills mentioned in this CV. Return a JSON object with:
{
  "technical_skills": ["skill1", "skill2", ...],
  "soft_skills": ["skill1", "skill2", ...],
  "languages": ["language1", "language2", ...],
  "certifications": ["cert1", "cert2", ...],
  "tools_and_technologies": ["tool1", "tool2", ...]
}
"""
        
        elif request.analysis_type == "experience_analysis":
            return base_context + """

Analyze the work experience and provide insights. Return a JSON object with:
{
  "total_years_experience": number,
  "experience_level": "entry/junior/mid/senior/lead/executive",
  "career_progression": "description of career growth",
  "industry_experience": ["industry1", "industry2", ...],
  "leadership_experience": true/false,
  "key_achievements": ["achievement1", "achievement2", ...]
}
"""
        
        elif request.analysis_type == "content_quality":
            return base_context + """

Evaluate the overall quality and completeness of this CV. Return a JSON object with:
{
  "completeness_score": number (0-100),
  "clarity_score": number (0-100),
  "professional_tone": number (0-100),
  "structure_quality": number (0-100),
  "missing_sections": ["section1", "section2", ...],
  "improvement_suggestions": ["suggestion1", "suggestion2", ...]
}
"""
        
        elif request.analysis_type == "job_matching":
            job_context = request.context.get("job_description", "") if request.context else ""
            return base_context + f"""

Job Description:
{job_context[:2000]}

Compare this CV against the job requirements. Return a JSON object with:
{{
  "match_score": number (0-100),
  "matching_skills": ["skill1", "skill2", ...],
  "missing_skills": ["skill1", "skill2", ...],
  "experience_relevance": number (0-100),
  "education_relevance": number (0-100),
  "overall_fit": "excellent/good/fair/poor",
  "recommendations": ["recommendation1", "recommendation2", ...]
}}
"""
        
        else:
            return base_context + """

Provide a general analysis of this CV. Return a JSON object with relevant insights.
"""
    
    def _parse_llm_response(self, response_text: str, analysis_type: str) -> Dict[str, Any]:
        """Parse LLM response and extract JSON"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # Fallback: return the raw response
                return {"raw_response": response_text, "parsed": False}
                
        except json.JSONDecodeError:
            logger.warning("Failed to parse LLM JSON response", response=response_text[:200])
            return {"raw_response": response_text, "parsed": False}
    
    async def health_check(self) -> bool:
        """Check if Ollama service is healthy"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/api/tags")
                return response.status_code == 200
        except Exception:
            return False


# Global instance
ollama_client = OllamaClient()
