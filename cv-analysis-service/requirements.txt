# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Text Extraction
PyMuPDF==1.23.8
python-docx==1.1.0
pdfplumber==0.10.3
python-magic==0.4.27

# NLP Libraries
spacy==3.7.2
transformers==4.35.2
torch==2.1.1
nltk==3.8.1
langdetect==1.0.9

# Hebrew NLP
hebrew-tokenizer==2.3.0

# Machine Learning
scikit-learn==1.3.2
numpy==1.24.4
pandas==2.1.3
joblib==1.3.2

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
redis==5.0.1

# HTTP Client
httpx==0.25.2
aiofiles==23.2.0

# LLM Integration
ollama==0.1.7

# Validation
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
psutil==5.9.6

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Environment
python-dotenv==1.0.0

# Hebrew Language Models (to be downloaded separately)
# he_core_news_sm @ https://github.com/explosion/spacy-models/releases/download/he_core_news_sm-3.7.0/he_core_news_sm-3.7.0-py3-none-any.whl
