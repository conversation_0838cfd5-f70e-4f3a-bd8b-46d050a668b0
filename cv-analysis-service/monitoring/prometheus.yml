global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'cvmatic-analysis'
    static_configs:
      - targets: ['cv-analysis:8000']
    metrics_path: '/api/v1/cv-analysis/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'cvmatic-health'
    static_configs:
      - targets: ['cv-analysis:8000']
    metrics_path: '/health'
    scrape_interval: 60s
    scrape_timeout: 10s
