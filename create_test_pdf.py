#!/usr/bin/env python3
"""
Create a simple test PDF for CV analysis testing
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

def create_test_cv():
    filename = "test_cv.pdf"
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(100, height - 100, "John Doe")
    
    # Contact info
    c.setFont("Helvetica", 12)
    c.drawString(100, height - 130, "Email: <EMAIL>")
    c.drawString(100, height - 150, "Phone: ******-123-4567")
    
    # Skills section
    c.setFont("Helvetica-Bold", 14)
    c.drawString(100, height - 200, "Skills:")
    c.setFont("Helvetica", 12)
    c.drawString(120, height - 220, "• Python Programming")
    c.drawString(120, height - 240, "• Machine Learning")
    c.drawString(120, height - 260, "• Data Analysis")
    c.drawString(120, height - 280, "• FastAPI")
    
    # Experience section
    c.setFont("Helvetica-Bold", 14)
    c.drawString(100, height - 320, "Experience:")
    c.setFont("Helvetica", 12)
    c.drawString(120, height - 340, "Software Engineer at Tech Corp (2020-2023)")
    c.drawString(120, height - 360, "• Developed web applications using Python")
    c.drawString(120, height - 380, "• Implemented machine learning models")
    
    # Education section
    c.setFont("Helvetica-Bold", 14)
    c.drawString(100, height - 420, "Education:")
    c.setFont("Helvetica", 12)
    c.drawString(120, height - 440, "Bachelor of Science in Computer Science")
    c.drawString(120, height - 460, "University of Technology (2016-2020)")
    
    c.save()
    print(f"Created {filename}")

if __name__ == "__main__":
    create_test_cv()
