{"name": "jest-watcher", "description": "Delightful JavaScript Testing.", "version": "30.0.4", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/test-result": "30.0.4", "@jest/types": "30.0.1", "@types/node": "*", "ansi-escapes": "^4.3.2", "chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.2", "string-length": "^4.0.2"}, "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-watcher"}, "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "homepage": "https://jestjs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5"}