{"version": 3, "file": "file-selector.umd.js", "sources": ["../../node_modules/tslib/tslib.es6.mjs", "../es2015/file.js", "../es2015/file-selector.js"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n};\n", "export const COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\nexport function toFileWithPath(file, path, h) {\n    const f = withMimeType(file);\n    const { webkitRelativePath } = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\nfunction withMimeType(file) {\n    const { name } = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop().toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\nfunction setObjProp(f, key, value) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    });\n}\n//# sourceMappingURL=file.js.map", "import { __awaiter } from \"tslib\";\nimport { toFileWithPath } from './file';\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport function fromEvent(evt) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n            return getDataTransferFiles(evt.dataTransfer, evt.type);\n        }\n        else if (isChangeEvt(evt)) {\n            return getInputFiles(evt);\n        }\n        else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n            return getFsHandleFiles(evt);\n        }\n        return [];\n    });\n}\nfunction isDataTransfer(value) {\n    return isObject(value);\n}\nfunction isChangeEvt(value) {\n    return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n    return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n    return fromList(evt.target.files).map(file => toFileWithPath(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const files = yield Promise.all(handles.map(h => h.getFile()));\n        return files.map(file => toFileWithPath(file));\n    });\n}\nfunction getDataTransferFiles(dt, type) {\n    return __awaiter(this, void 0, void 0, function* () {\n        // IE11 does not support dataTransfer.items\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n        if (dt.items) {\n            const items = fromList(dt.items)\n                .filter(item => item.kind === 'file');\n            // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n            // only 'dragstart' and 'drop' has access to the data (source node)\n            if (type !== 'drop') {\n                return items;\n            }\n            const files = yield Promise.all(items.map(toFilePromises));\n            return noIgnoredFiles(flatten(files));\n        }\n        return noIgnoredFiles(fromList(dt.files)\n            .map(file => toFileWithPath(file)));\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    if (items === null) {\n        return [];\n    }\n    const files = [];\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n    const entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\nfunction fromDataTransferItem(item, entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n        // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n        //\n        // See:\n        // - https://issues.chromium.org/issues/40186242\n        // - https://github.com/react-dropzone/react-dropzone/issues/1397\n        if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n            const h = yield item.getAsFileSystemHandle();\n            if (h === null) {\n                throw new Error(`${item} is not a File`);\n            }\n            // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n            // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n            if (h !== undefined) {\n                const file = yield h.getFile();\n                file.handle = h;\n                return toFileWithPath(file);\n            }\n        }\n        const file = item.getAsFile();\n        if (!file) {\n            throw new Error(`${item} is not a File`);\n        }\n        const fwp = toFileWithPath(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n        return fwp;\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    const reader = entry.createReader();\n    return new Promise((resolve, reject) => {\n        const entries = [];\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries((batch) => __awaiter(this, void 0, void 0, function* () {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = yield Promise.all(entries);\n                        resolve(files);\n                    }\n                    catch (err) {\n                        reject(err);\n                    }\n                }\n                else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n                    // Continue reading\n                    readEntries();\n                }\n            }), (err) => {\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            entry.file((file) => {\n                const fwp = toFileWithPath(file, entry.fullPath);\n                resolve(fwp);\n            }, (err) => {\n                reject(err);\n            });\n        });\n    });\n}\n//# sourceMappingURL=file-selector.js.map"], "names": [], "mappings": ";;;;;;EAAA;EACA;AACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;AAkGA;EACO,SAAS,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;EAC7D,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAC9G,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE;EAC7D,MAAM,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACjG,MAAM,SAAS,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACpG,MAAM,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;EACpH,MAAM,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EAC5E,GAAG,CAAC,CAAC;EACL,CAAC;AAmMD;EACuB,OAAO,eAAe,KAAK,UAAU,GAAG,eAAe,GAAG,UAAU,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE;EACvH,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;EAC7B,EAAE,OAAO,CAAC,CAAC,IAAI,GAAG,iBAAiB,EAAE,CAAC,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,UAAU,GAAG,UAAU,EAAE,CAAC,CAAC;EACnF;;ECjUO,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC;EACzC;EACA,IAAI,CAAC,KAAK,EAAE,8CAA8C,CAAC;EAC3D,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,IAAI,EAAE,6BAA6B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,6BAA6B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,qCAAqC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,sCAAsC,CAAC;EACnD,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,OAAO,EAAE,yBAAyB,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,OAAO,EAAE,6BAA6B,CAAC;EAC5C,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC;EAC3B,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC;EAC5B,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,6DAA6D,CAAC;EAC1E,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;EACtD,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC;EACvC,IAAI,CAAC,aAAa,EAAE,8BAA8B,CAAC;EACnD,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,SAAS,EAAE,yBAAyB,CAAC;EAC1C,IAAI,CAAC,aAAa,EAAE,6BAA6B,CAAC;EAClD,IAAI,CAAC,SAAS,EAAE,yBAAyB,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,sCAAsC,CAAC;EACnD,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,uCAAuC,CAAC;EACpD,IAAI,CAAC,KAAK,EAAE,uCAAuC,CAAC;EACpD,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,MAAM,EAAE,mCAAmC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,4BAA4B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,0BAA0B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,qCAAqC,CAAC;EAClD,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;EAC9B,IAAI,CAAC,QAAQ,EAAE,0BAA0B,CAAC;EAC1C,IAAI,CAAC,IAAI,EAAE,oBAAoB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC;EACrB,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,QAAQ,EAAE,8CAA8C,CAAC;EAC9D,IAAI,CAAC,QAAQ,EAAE,kDAAkD,CAAC;EAClE,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;EACtB,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,OAAO,EAAE,uBAAuB,CAAC;EACtC,IAAI,CAAC,SAAS,EAAE,8BAA8B,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,OAAO,EAAE,oCAAoC,CAAC;EACnD,IAAI,CAAC,OAAO,EAAE,6BAA6B,CAAC;EAC5C,IAAI,CAAC,OAAO,EAAE,4BAA4B,CAAC;EAC3C,IAAI,CAAC,OAAO,EAAE,yBAAyB,CAAC;EACxC,IAAI,CAAC,OAAO,EAAE,yBAAyB,CAAC;EACxC,IAAI,CAAC,OAAO,EAAE,wBAAwB,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,OAAO,EAAE,8BAA8B,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,MAAM,EAAE,4BAA4B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,wDAAwD,CAAC;EACrE,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,OAAO,EAAE,0BAA0B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,wCAAwC,CAAC;EACtD,IAAI,CAAC,MAAM,EAAE,uCAAuC,CAAC;EACrD,IAAI,CAAC,MAAM,EAAE,wCAAwC,CAAC;EACtD,IAAI,CAAC,MAAM,EAAE,wCAAwC,CAAC;EACtD,IAAI,CAAC,MAAM,EAAE,+BAA+B,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;EACtD,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,YAAY,EAAE,gCAAgC,CAAC;EACpD,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,0CAA0C,CAAC;EACvD,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,IAAI,EAAE,sBAAsB,CAAC;EAClC,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC;EAC7C,IAAI,CAAC,UAAU,EAAE,0BAA0B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,QAAQ,EAAE,0BAA0B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,8BAA8B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,0BAA0B,EAAE,kCAAkC,CAAC;EACpE,IAAI,CAAC,MAAM,EAAE,0BAA0B,CAAC;EACxC,IAAI,CAAC,OAAO,EAAE,0BAA0B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,kDAAkD,CAAC;EAChE,IAAI,CAAC,MAAM,EAAE,yEAAyE,CAAC;EACvF,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,kDAAkD,CAAC;EAChE,IAAI,CAAC,MAAM,EAAE,yEAAyE,CAAC;EACvF,IAAI,CAAC,IAAI,EAAE,yBAAyB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,0BAA0B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,WAAW,EAAE,2BAA2B,CAAC;EAC9C,IAAI,CAAC,WAAW,EAAE,2BAA2B,CAAC;EAC9C,IAAI,CAAC,WAAW,EAAE,2BAA2B,CAAC;EAC9C,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,WAAW,EAAE,2BAA2B,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,IAAI,EAAE,0BAA0B,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,GAAG,EAAE,gBAAgB,CAAC;EAC3B,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,yCAAyC,CAAC;EACvD,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,WAAW,EAAE,wCAAwC,CAAC;EAC3D,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,IAAI,EAAE,kBAAkB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC;EAC3B,IAAI,CAAC,IAAI,EAAE,4BAA4B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,IAAI,EAAE,6CAA6C,CAAC;EACzD,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,OAAO,EAAE,4BAA4B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,qDAAqD,CAAC;EAClE,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,MAAM,EAAE,2BAA2B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,MAAM,EAAE,sCAAsC,CAAC;EACpD,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,SAAS,EAAE,sBAAsB,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;EACtD,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,UAAU,EAAE,wBAAwB,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,QAAQ,EAAE,0BAA0B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,OAAO,EAAE,sBAAsB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,QAAQ,EAAE,yCAAyC,CAAC;EACzD,IAAI,CAAC,SAAS,EAAE,0CAA0C,CAAC;EAC3D,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,qCAAqC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,IAAI,EAAE,kBAAkB,CAAC;EAC9B,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC;EACrB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC;EAC3B,IAAI,CAAC,MAAM,EAAE,2BAA2B,CAAC;EACzC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;EACtB,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,yBAAyB,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,yBAAyB,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,MAAM,EAAE,4BAA4B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC;EAC3B,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;EACtD,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,4CAA4C,CAAC;EACzD,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,OAAO,EAAE,uBAAuB,CAAC;EACtC,IAAI,CAAC,SAAS,EAAE,oCAAoC,CAAC;EACrD,IAAI,CAAC,MAAM,EAAE,uCAAuC,CAAC;EACrD,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,wCAAwC,CAAC;EACrD,IAAI,CAAC,KAAK,EAAE,uCAAuC,CAAC;EACpD,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;EACtD,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,6CAA6C,CAAC;EAC1D,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,SAAS,EAAE,iCAAiC,CAAC;EAClD,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,8BAA8B,CAAC;EAC5C,IAAI,CAAC,MAAM,EAAE,oCAAoC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC;EAClC,IAAI,CAAC,QAAQ,EAAE,qBAAqB,CAAC;EACrC;EACA,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC;EAClC,IAAI,CAAC,QAAQ,EAAE,yBAAyB,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,QAAQ,EAAE,4BAA4B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,sCAAsC,CAAC;EACnD,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,MAAM,EAAE,6BAA6B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,QAAQ,EAAE,6BAA6B,CAAC;EAC7C,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,oDAAoD,CAAC;EACjE,IAAI,CAAC,KAAK,EAAE,yDAAyD,CAAC;EACtE,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,QAAQ,EAAE,oCAAoC,CAAC;EACpD,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,UAAU,EAAE,4BAA4B,CAAC;EAC9C,IAAI,CAAC,SAAS,EAAE,4BAA4B,CAAC;EAC7C,IAAI,CAAC,WAAW,EAAE,mBAAmB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,SAAS,EAAE,sBAAsB,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,4BAA4B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,+BAA+B,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,IAAI,EAAE,yBAAyB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,yBAAyB,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,OAAO,EAAE,4BAA4B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,UAAU,EAAE,eAAe,CAAC;EACjC,IAAI,CAAC,QAAQ,EAAE,wBAAwB,CAAC;EACxC,IAAI,CAAC,IAAI,EAAE,yBAAyB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC;EAC3B,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,OAAO,EAAE,2BAA2B,CAAC;EAC1C,IAAI,CAAC,UAAU,EAAE,0BAA0B,CAAC;EAC5C,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,wCAAwC,CAAC;EACrD,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,sCAAsC,CAAC;EACnD,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,gCAAgC,CAAC;EAC9C,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC;EAC/B,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,qCAAqC,CAAC;EACnD,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,MAAM,EAAE,yBAAyB,CAAC;EACvC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;EACxB,IAAI,CAAC,OAAO,EAAE,oCAAoC,CAAC;EACnD,IAAI,CAAC,OAAO,EAAE,4BAA4B,CAAC;EAC3C,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,6BAA6B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,yBAAyB,CAAC;EACvC,IAAI,CAAC,UAAU,EAAE,wCAAwC,CAAC;EAC1D,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,QAAQ,EAAE,8CAA8C,CAAC;EAC9D,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;EACrB,IAAI,CAAC,IAAI,EAAE,yBAAyB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,IAAI,EAAE,sBAAsB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,OAAO,EAAE,mCAAmC,CAAC;EAClD,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,IAAI,EAAE,qBAAqB,CAAC;EACjC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,IAAI,EAAE,uBAAuB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,SAAS,EAAE,wCAAwC,CAAC;EACzD,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,mCAAmC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,6CAA6C,CAAC;EAC1D,IAAI,CAAC,KAAK,EAAE,0CAA0C,CAAC;EACvD,IAAI,CAAC,KAAK,EAAE,4CAA4C,CAAC;EACzD,IAAI,CAAC,MAAM,EAAE,qDAAqD,CAAC;EACnE,IAAI,CAAC,KAAK,EAAE,6CAA6C,CAAC;EAC1D,IAAI,CAAC,KAAK,EAAE,0CAA0C,CAAC;EACvD,IAAI,CAAC,KAAK,EAAE,gDAAgD,CAAC;EAC7D,IAAI,CAAC,KAAK,EAAE,iDAAiD,CAAC;EAC9D,IAAI,CAAC,KAAK,EAAE,gDAAgD,CAAC;EAC7D,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;EACtD,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC;EACjC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,OAAO,EAAE,uBAAuB,CAAC;EACtC,IAAI,CAAC,QAAQ,EAAE,qBAAqB,CAAC;EACrC,IAAI,CAAC,QAAQ,EAAE,qBAAqB,CAAC;EACrC,IAAI,CAAC,QAAQ,EAAE,qBAAqB,CAAC;EACrC,IAAI,CAAC,SAAS,EAAE,qBAAqB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC;EAC3B,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,wCAAwC,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,mDAAmD,CAAC;EACnE,IAAI,CAAC,KAAK,EAAE,wCAAwC,CAAC;EACrD,IAAI,CAAC,KAAK,EAAE,mDAAmD,CAAC;EAChE,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,sDAAsD,CAAC;EACnE,IAAI,CAAC,KAAK,EAAE,6CAA6C,CAAC;EAC1D,IAAI,CAAC,KAAK,EAAE,mDAAmD,CAAC;EAChE,IAAI,CAAC,KAAK,EAAE,0DAA0D,CAAC;EACvE,IAAI,CAAC,KAAK,EAAE,yDAAyD,CAAC;EACtE,IAAI,CAAC,KAAK,EAAE,kDAAkD,CAAC;EAC/D,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;EACtD,IAAI,CAAC,GAAG,EAAE,eAAe,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,OAAO,EAAE,oCAAoC,CAAC;EACnD,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,MAAM,EAAE,8BAA8B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,OAAO,EAAE,0BAA0B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC;EAC3B,IAAI,CAAC,OAAO,EAAE,4BAA4B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,MAAM,EAAE,yBAAyB,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,yBAAyB,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,gCAAgC,CAAC;EAC9C,IAAI,CAAC,OAAO,EAAE,yBAAyB,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC;EAC3B,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,SAAS,EAAE,0BAA0B,CAAC;EAC3C,IAAI,CAAC,QAAQ,EAAE,8BAA8B,CAAC;EAC9C,IAAI,CAAC,IAAI,EAAE,oBAAoB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,IAAI,EAAE,oBAAoB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,SAAS,EAAE,kCAAkC,CAAC;EACnD,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,MAAM,EAAE,4DAA4D,CAAC;EAC1E,IAAI,CAAC,MAAM,EAAE,uEAAuE,CAAC;EACrF,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,MAAM,EAAE,qDAAqD,CAAC;EACnE,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,MAAM,EAAE,yDAAyD,CAAC;EACvE,IAAI,CAAC,MAAM,EAAE,wEAAwE,CAAC;EACtF,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,4DAA4D,CAAC;EAC1E,IAAI,CAAC,MAAM,EAAE,2EAA2E,CAAC;EACzF,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,OAAO,EAAE,4BAA4B,CAAC;EAC3C,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,SAAS,EAAE,sBAAsB,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,MAAM,EAAE,2BAA2B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,uCAAuC,CAAC;EACpD,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,MAAM,EAAE,uBAAuB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,2BAA2B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,WAAW,EAAE,uCAAuC,CAAC;EAC1D,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,MAAM,EAAE,6BAA6B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,qCAAqC,CAAC;EAClD,IAAI,CAAC,IAAI,EAAE,gCAAgC,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,qCAAqC,CAAC;EAClD,IAAI,CAAC,IAAI,EAAE,sBAAsB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,uCAAuC,CAAC;EACpD,IAAI,CAAC,MAAM,EAAE,kCAAkC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,qCAAqC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,MAAM,EAAE,qCAAqC,CAAC;EACnD,IAAI,CAAC,MAAM,EAAE,oCAAoC,CAAC;EAClD,IAAI,CAAC,IAAI,EAAE,0BAA0B,CAAC;EACtC,IAAI,CAAC,IAAI,EAAE,8BAA8B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,MAAM,EAAE,2BAA2B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,QAAQ,EAAE,8BAA8B,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,2BAA2B,CAAC;EACzC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC;EAC3B,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,sCAAsC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC;EAC3B,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,sCAAsC,CAAC;EACnD,IAAI,CAAC,MAAM,EAAE,iCAAiC,CAAC;EAC/C,IAAI,CAAC,MAAM,EAAE,iCAAiC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,qCAAqC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,MAAM,EAAE,2BAA2B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,QAAQ,EAAE,uBAAuB,CAAC;EACvC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,QAAQ,EAAE,oCAAoC,CAAC;EACpD,IAAI,CAAC,QAAQ,EAAE,yCAAyC,CAAC;EACzD,IAAI,CAAC,WAAW,EAAE,sCAAsC,CAAC;EACzD,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,4CAA4C,CAAC;EACzD,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,IAAI,EAAE,kBAAkB,CAAC;EAC9B,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,MAAM,EAAE,iCAAiC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,MAAM,EAAE,qDAAqD,CAAC;EACnE,IAAI,CAAC,MAAM,EAAE,oEAAoE,CAAC;EAClF,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,IAAI,EAAE,qCAAqC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,OAAO,EAAE,mCAAmC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,IAAI,EAAE,0BAA0B,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,IAAI,EAAE,sCAAsC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,uCAAuC,CAAC;EACpD,IAAI,CAAC,KAAK,EAAE,uCAAuC,CAAC;EACpD,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,0CAA0C,CAAC;EACvD,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;EAC9B,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC;EACnC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;EACtD,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC;EAC3B,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,MAAM,EAAE,8BAA8B,CAAC;EAC5C,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC;EACxC,IAAI,CAAC,QAAQ,EAAE,sBAAsB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,SAAS,EAAE,sBAAsB,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,uCAAuC,CAAC;EACpD,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC;EACvB,IAAI,CAAC,IAAI,EAAE,0BAA0B,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,QAAQ,EAAE,uBAAuB,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,2CAA2C,CAAC;EACxD,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,MAAM,EAAE,4BAA4B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,IAAI,EAAE,gCAAgC,CAAC;EAC5C,IAAI,CAAC,SAAS,EAAE,+BAA+B,CAAC;EAChD,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,WAAW,EAAE,qBAAqB,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,MAAM,EAAE,uBAAuB,CAAC;EACrC,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC;EACxC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,MAAM,EAAE,gCAAgC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,SAAS,EAAE,0BAA0B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,sCAAsC,CAAC;EACnD,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,oCAAoC,CAAC;EACjD,IAAI,CAAC,MAAM,EAAE,oCAAoC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,OAAO,EAAE,gCAAgC,CAAC;EAC/C,IAAI,CAAC,OAAO,EAAE,wBAAwB,CAAC;EACvC,IAAI,CAAC,OAAO,EAAE,yCAAyC,CAAC;EACxD,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,8BAA8B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,UAAU,EAAE,uBAAuB,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,0BAA0B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC;EAC7B,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC;EAC7B,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,2BAA2B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,2BAA2B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC;EACtC,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC;EACtC,IAAI,CAAC,MAAM,EAAE,uBAAuB,CAAC;EACrC,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,+BAA+B,CAAC;EAC7C,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,MAAM,EAAE,kCAAkC,CAAC;EAChD,IAAI,CAAC,MAAM,EAAE,0BAA0B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,+BAA+B,CAAC;EAC7C,IAAI,CAAC,cAAc,EAAE,uCAAuC,CAAC;EAC7D,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC;EAC3B,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC;EAC3B,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,mBAAmB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,MAAM,EAAE,+BAA+B,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,qCAAqC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;EACvB,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,MAAM,EAAE,0BAA0B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,8BAA8B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;EAC1B,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,uCAAuC,CAAC;EACpD,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,QAAQ,EAAE,qCAAqC,CAAC;EACrD,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,aAAa,EAAE,2BAA2B,CAAC;EAChD,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;EAC1B,IAAI,CAAC,IAAI,EAAE,4BAA4B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC;EAC3B,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC;EACtC,IAAI,CAAC,OAAO,EAAE,gCAAgC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC;EAC3B,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC;EAC1C,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;EACzB,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,UAAU,EAAE,0BAA0B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC;EAC5B,IAAI,CAAC,MAAM,EAAE,uBAAuB,CAAC;EACrC,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;EAC9B,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC;EAC/B,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC;EAC7B,IAAI,CAAC,KAAK,EAAE,8BAA8B,CAAC;EAC3C,IAAI,CAAC,KAAK,EAAE,qCAAqC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC;EAChD,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,uBAAuB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,4CAA4C,CAAC;EACzD,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,2BAA2B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAC5C,IAAI,CAAC,OAAO,EAAE,sBAAsB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,qCAAqC,CAAC;EAClD,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,MAAM,EAAE,4BAA4B,CAAC;EAC1C,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,OAAO,EAAE,uBAAuB,CAAC;EACtC,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC;EAC/B,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,gDAAgD,CAAC;EAC9D,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,uDAAuD,CAAC;EACrE,IAAI,CAAC,MAAM,EAAE,gDAAgD,CAAC;EAC9D,IAAI,CAAC,MAAM,EAAE,mEAAmE,CAAC;EACjF,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,MAAM,EAAE,mDAAmD,CAAC;EACjE,IAAI,CAAC,MAAM,EAAE,sEAAsE,CAAC;EACpF,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC;EACvC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;EACtB,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,IAAI,EAAE,4BAA4B,CAAC;EACxC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,yBAAyB,CAAC;EACtC,IAAI,CAAC,KAAK,EAAE,uBAAuB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC;EACrC,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,kCAAkC,CAAC;EAC/C,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC;EACzC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,iCAAiC,CAAC;EAC9C,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACjC,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC;EAC7B,IAAI,CAAC,IAAI,EAAE,kBAAkB,CAAC;EAC9B,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;EACzB,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;EAChC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC;EACxB,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,GAAG,EAAE,wBAAwB,CAAC;EACnC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACpC,IAAI,CAAC,KAAK,EAAE,gCAAgC,CAAC;EAC7C,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;EAC9B,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAClC,IAAI,CAAC,MAAM,EAAE,qBAAqB,CAAC;EACnC,IAAI,CAAC,KAAK,EAAE,4CAA4C,CAAC;EACzD,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC;EAC/B,CAAC,CAAC,CAAC;EACI,SAAS,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE;EAC9C,IAAI,MAAM,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;EACjC,IAAI,MAAM,EAAE,kBAAkB,EAAE,GAAG,IAAI,CAAC;EACxC,IAAI,MAAM,CAAC,GAAG,OAAO,IAAI,KAAK,QAAQ;EACtC,UAAU,IAAI;EACd;EACA;EACA;EACA,UAAU,OAAO,kBAAkB,KAAK,QAAQ,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC;EACjF,cAAc,kBAAkB;EAChC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAC/B,IAAI,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;EACpC,QAAQ,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;EACjC,KAAK;EASL;EACA,IAAI,UAAU,CAAC,CAAC,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;EACrC,IAAI,OAAO,CAAC,CAAC;EACb,CAAC;EACD,SAAS,YAAY,CAAC,IAAI,EAAE;EAC5B,IAAI,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;EAC1B,IAAI,MAAM,YAAY,GAAG,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9D,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;EACpC,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;EACnC,aAAa,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;EACjC,QAAQ,MAAM,IAAI,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAChD,QAAQ,IAAI,IAAI,EAAE;EAClB,YAAY,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;EAChD,gBAAgB,KAAK,EAAE,IAAI;EAC3B,gBAAgB,QAAQ,EAAE,KAAK;EAC/B,gBAAgB,YAAY,EAAE,KAAK;EACnC,gBAAgB,UAAU,EAAE,IAAI;EAChC,aAAa,CAAC,CAAC;EACf,SAAS;EACT,KAAK;EACL,IAAI,OAAO,IAAI,CAAC;EAChB,CAAC;EACD,SAAS,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE;EACnC,IAAI,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,GAAG,EAAE;EAClC,QAAQ,KAAK;EACb,QAAQ,QAAQ,EAAE,KAAK;EACvB,QAAQ,YAAY,EAAE,KAAK;EAC3B,QAAQ,UAAU,EAAE,IAAI;EACxB,KAAK,CAAC,CAAC;EACP;;ECpuCA,MAAM,eAAe,GAAG;EACxB;EACA,IAAI,WAAW;EACf,IAAI,WAAW;EACf,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS,SAAS,CAAC,GAAG,EAAE;EAC/B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACxD,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;EAC/D,YAAY,OAAO,oBAAoB,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;EACpE,SAAS;EACT,aAAa,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;EACnC,YAAY,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC;EACtC,SAAS;EACT,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,CAAC,EAAE;EACnH,YAAY,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC;EACzC,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC;EAClB,KAAK,CAAC,CAAC;EACP,CAAC;EACD,SAAS,cAAc,CAAC,KAAK,EAAE;EAC/B,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;EAC3B,CAAC;EACD,SAAS,WAAW,CAAC,KAAK,EAAE;EAC5B,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;EACrD,CAAC;EACD,SAAS,QAAQ,CAAC,CAAC,EAAE;EACrB,IAAI,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC;EAC/C,CAAC;EACD,SAAS,aAAa,CAAC,GAAG,EAAE;EAC5B,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;EACxE,CAAC;EACD;EACA,SAAS,gBAAgB,CAAC,OAAO,EAAE;EACnC,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACxD,QAAQ,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;EACvE,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;EACvD,KAAK,CAAC,CAAC;EACP,CAAC;EACD,SAAS,oBAAoB,CAAC,EAAE,EAAE,IAAI,EAAE;EACxC,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACxD;EACA;EACA,QAAQ,IAAI,EAAE,CAAC,KAAK,EAAE;EACtB,YAAY,MAAM,KAAK,GAAG,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;EAC5C,iBAAiB,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;EACtD;EACA;EACA,YAAY,IAAI,IAAI,KAAK,MAAM,EAAE;EACjC,gBAAgB,OAAO,KAAK,CAAC;EAC7B,aAAa;EACb,YAAY,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;EACvE,YAAY,OAAO,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;EAClD,SAAS;EACT,QAAQ,OAAO,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;EAChD,aAAa,GAAG,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAChD,KAAK,CAAC,CAAC;EACP,CAAC;EACD,SAAS,cAAc,CAAC,KAAK,EAAE;EAC/B,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3E,CAAC;EACD;EACA;EACA;EACA;EACA,SAAS,QAAQ,CAAC,KAAK,EAAE;EACzB,IAAI,IAAI,KAAK,KAAK,IAAI,EAAE;EACxB,QAAQ,OAAO,EAAE,CAAC;EAClB,KAAK;EACL,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;EACrB;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC3C,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACzB,KAAK;EACL,IAAI,OAAO,KAAK,CAAC;EACjB,CAAC;EACD;EACA,SAAS,cAAc,CAAC,IAAI,EAAE;EAC9B,IAAI,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,UAAU,EAAE;EACrD,QAAQ,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;EAC1C,KAAK;EACL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;EAC1C;EACA;EACA;EACA,IAAI,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE;EACpC,QAAQ,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC;EACnC,KAAK;EACL,IAAI,OAAO,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;EAC7C,CAAC;EACD,SAAS,OAAO,CAAC,KAAK,EAAE;EACxB,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;EACxC,QAAQ,GAAG,GAAG;EACd,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EAC5D,KAAK,EAAE,EAAE,CAAC,CAAC;EACX,CAAC;EACD,SAAS,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE;EAC3C,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACxD,QAAQ,IAAI,EAAE,CAAC;EACf;EACA;EACA;EACA;EACA;EACA;EACA,QAAQ,IAAI,UAAU,CAAC,eAAe,IAAI,OAAO,IAAI,CAAC,qBAAqB,KAAK,UAAU,EAAE;EAC5F,YAAY,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;EACzD,YAAY,IAAI,CAAC,KAAK,IAAI,EAAE;EAC5B,gBAAgB,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;EACzD,aAAa;EACb;EACA;EACA,YAAY,IAAI,CAAC,KAAK,SAAS,EAAE;EACjC,gBAAgB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;EAC/C,gBAAgB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EAChC,gBAAgB,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;EAC5C,aAAa;EACb,SAAS;EACT,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;EACtC,QAAQ,IAAI,CAAC,IAAI,EAAE;EACnB,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;EACrD,SAAS;EACT,QAAQ,MAAM,GAAG,GAAG,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,QAAQ,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;EACzJ,QAAQ,OAAO,GAAG,CAAC;EACnB,KAAK,CAAC,CAAC;EACP,CAAC;EACD;EACA,SAAS,SAAS,CAAC,KAAK,EAAE;EAC1B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACxD,QAAQ,OAAO,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;EAC9E,KAAK,CAAC,CAAC;EACP,CAAC;EACD;EACA,SAAS,YAAY,CAAC,KAAK,EAAE;EAC7B,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;EACxC,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;EAC5C,QAAQ,MAAM,OAAO,GAAG,EAAE,CAAC;EAC3B,QAAQ,SAAS,WAAW,GAAG;EAC/B;EACA;EACA,YAAY,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACvF,gBAAgB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;EACnC;EACA,oBAAoB,IAAI;EACxB,wBAAwB,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;EACjE,wBAAwB,OAAO,CAAC,KAAK,CAAC,CAAC;EACvC,qBAAqB;EACrB,oBAAoB,OAAO,GAAG,EAAE;EAChC,wBAAwB,MAAM,CAAC,GAAG,CAAC,CAAC;EACpC,qBAAqB;EACrB,iBAAiB;EACjB,qBAAqB;EACrB,oBAAoB,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;EACpE,oBAAoB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACxC;EACA,oBAAoB,WAAW,EAAE,CAAC;EAClC,iBAAiB;EACjB,aAAa,CAAC,EAAE,CAAC,GAAG,KAAK;EACzB,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC;EAC5B,aAAa,CAAC,CAAC;EACf,SAAS;EACT,QAAQ,WAAW,EAAE,CAAC;EACtB,KAAK,CAAC,CAAC;EACP,CAAC;EACD;EACA,SAAS,aAAa,CAAC,KAAK,EAAE;EAC9B,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACxD,QAAQ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;EAChD,YAAY,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK;EACjC,gBAAgB,MAAM,GAAG,GAAG,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;EACjE,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC;EAC7B,aAAa,EAAE,CAAC,GAAG,KAAK;EACxB,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC;EAC5B,aAAa,CAAC,CAAC;EACf,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP;;;;;;;;", "x_google_ignoreList": [0]}