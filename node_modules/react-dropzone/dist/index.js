!function(a,i){"object"==typeof exports&&"undefined"!=typeof module?i(exports,require("react"),require("prop-types")):"function"==typeof define&&define.amd?define(["exports","react","prop-types"],i):i((a="undefined"!=typeof globalThis?globalThis:a||self).reactDropzone={},a.<PERSON>act,a.PropTypes)}(this,(function(a,i,t){"use strict";function n(a){return a&&"object"==typeof a&&"default"in a?a:{default:a}}var p=n(i),e=n(t);function o(a,i){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(a);i&&(n=n.filter((function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable}))),t.push.apply(t,n)}return t}function c(a){for(var i=1;i<arguments.length;i++){var t=null!=arguments[i]?arguments[i]:{};i%2?o(Object(t),!0).forEach((function(i){l(a,i,t[i])})):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach((function(i){Object.defineProperty(a,i,Object.getOwnPropertyDescriptor(t,i))}))}return a}function l(a,i,t){return i in a?Object.defineProperty(a,i,{value:t,enumerable:!0,configurable:!0,writable:!0}):a[i]=t,a}function r(a,i){if(null==a)return{};var t,n,p=function(a,i){if(null==a)return{};var t,n,p={},e=Object.keys(a);for(n=0;n<e.length;n++)t=e[n],i.indexOf(t)>=0||(p[t]=a[t]);return p}(a,i);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(a);for(n=0;n<e.length;n++)t=e[n],i.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(a,t)&&(p[t]=a[t])}return p}function d(a,i){return function(a){if(Array.isArray(a))return a}(a)||function(a,i){var t=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null==t)return;var n,p,e=[],o=!0,c=!1;try{for(t=t.call(a);!(o=(n=t.next()).done)&&(e.push(n.value),!i||e.length!==i);o=!0);}catch(a){c=!0,p=a}finally{try{o||null==t.return||t.return()}finally{if(c)throw p}}return e}(a,i)||m(a,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(a){return function(a){if(Array.isArray(a))return v(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||m(a)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(a,i){if(a){if("string"==typeof a)return v(a,i);var t=Object.prototype.toString.call(a).slice(8,-1);return"Object"===t&&a.constructor&&(t=a.constructor.name),"Map"===t||"Set"===t?Array.from(a):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?v(a,i):void 0}}function v(a,i){(null==i||i>a.length)&&(i=a.length);for(var t=0,n=new Array(i);t<i;t++)n[t]=a[t];return n}function x(a,i,t,n){return new(t||(t=Promise))((function(p,e){function o(a){try{l(n.next(a))}catch(a){e(a)}}function c(a){try{l(n.throw(a))}catch(a){e(a)}}function l(a){var i;a.done?p(a.value):(i=a.value,i instanceof t?i:new t((function(a){a(i)}))).then(o,c)}l((n=n.apply(a,i||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;const u=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function f(a,i,t){const n=function(a){const{name:i}=a;if(i&&-1!==i.lastIndexOf(".")&&!a.type){const t=i.split(".").pop().toLowerCase(),n=u.get(t);n&&Object.defineProperty(a,"type",{value:n,writable:!1,configurable:!1,enumerable:!0})}return a}(a),{webkitRelativePath:p}=a,e="string"==typeof i?i:"string"==typeof p&&p.length>0?p:`./${a.name}`;return"string"!=typeof n.path&&g(n,"path",e),void 0!==t&&Object.defineProperty(n,"handle",{value:t,writable:!1,configurable:!1,enumerable:!0}),g(n,"relativePath",e),n}function g(a,i,t){Object.defineProperty(a,i,{value:t,writable:!1,configurable:!1,enumerable:!0})}const b=[".DS_Store","Thumbs.db"];function h(a){return"object"==typeof a&&null!==a}function w(a){return a.filter((a=>-1===b.indexOf(a.name)))}function y(a){if(null===a)return[];const i=[];for(let t=0;t<a.length;t++){const n=a[t];i.push(n)}return i}function k(a){if("function"!=typeof a.webkitGetAsEntry)return z(a);const i=a.webkitGetAsEntry();return i&&i.isDirectory?F(i):z(a,i)}function j(a){return a.reduce(((a,i)=>[...a,...Array.isArray(i)?j(i):[i]]),[])}function z(a,i){var t;if("function"==typeof a.getAsFileSystemHandle)return a.getAsFileSystemHandle().then((a=>x(this,void 0,void 0,(function*(){const i=yield a.getFile();return i.handle=a,f(i)}))));const n=a.getAsFile();if(!n)return Promise.reject(`${a} is not a File`);const p=f(n,null!==(t=null==i?void 0:i.fullPath)&&void 0!==t?t:void 0);return Promise.resolve(p)}function D(a){return x(this,void 0,void 0,(function*(){return a.isDirectory?F(a):function(a){return x(this,void 0,void 0,(function*(){return new Promise(((i,t)=>{a.file((t=>{const n=f(t,a.fullPath);i(n)}),(a=>{t(a)}))}))}))}(a)}))}function F(a){const i=a.createReader();return new Promise(((a,t)=>{const n=[];!function p(){i.readEntries((i=>x(this,void 0,void 0,(function*(){if(i.length){const a=Promise.all(i.map(D));n.push(a),p()}else try{const i=yield Promise.all(n);a(i)}catch(a){t(a)}}))),(a=>{t(a)}))}()}))}var q=function(a,i){if(a&&i){var t=Array.isArray(i)?i:i.split(",");if(0===t.length)return!0;var n=a.name||"",p=(a.type||"").toLowerCase(),e=p.replace(/\/.*$/,"");return t.some((function(a){var i=a.trim().toLowerCase();return"."===i.charAt(0)?n.toLowerCase().endsWith(i):i.endsWith("/*")?e===i.replace(/\/.*$/,""):p===i}))}return!0},E=q,O="file-invalid-type",A="file-too-large",P="file-too-small",S="too-many-files",C={FileInvalidType:O,FileTooLarge:A,FileTooSmall:P,TooManyFiles:S},R=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",i=a.split(","),t=i.length>1?"one of ".concat(i.join(", ")):i[0];return{code:O,message:"File type must be ".concat(t)}},T=function(a){return{code:A,message:"File is larger than ".concat(a," ").concat(1===a?"byte":"bytes")}},I=function(a){return{code:P,message:"File is smaller than ".concat(a," ").concat(1===a?"byte":"bytes")}},M={code:S,message:"Too many files"};function L(a,i){var t="application/x-moz-file"===a.type||E(a,i);return[t,t?null:R(i)]}function _(a,i,t){if(B(a.size))if(B(i)&&B(t)){if(a.size>t)return[!1,T(t)];if(a.size<i)return[!1,I(i)]}else{if(B(i)&&a.size<i)return[!1,I(i)];if(B(t)&&a.size>t)return[!1,T(t)]}return[!0,null]}function B(a){return null!=a}function K(a){var i=a.files,t=a.accept,n=a.minSize,p=a.maxSize,e=a.multiple,o=a.maxFiles,c=a.validator;return!(!e&&i.length>1||e&&o>=1&&i.length>o)&&i.every((function(a){var i=d(L(a,t),1)[0],e=d(_(a,n,p),1)[0],o=c?c(a):null;return i&&e&&!o}))}function $(a){return"function"==typeof a.isPropagationStopped?a.isPropagationStopped():void 0!==a.cancelBubble&&a.cancelBubble}function H(a){return a.dataTransfer?Array.prototype.some.call(a.dataTransfer.types,(function(a){return"Files"===a||"application/x-moz-file"===a})):!!a.target&&!!a.target.files}function U(a){a.preventDefault()}function W(a){return-1!==a.indexOf("MSIE")||-1!==a.indexOf("Trident/")}function G(a){return-1!==a.indexOf("Edge/")}function N(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return W(a)||G(a)}function Y(){for(var a=arguments.length,i=new Array(a),t=0;t<a;t++)i[t]=arguments[t];return function(a){for(var t=arguments.length,n=new Array(t>1?t-1:0),p=1;p<t;p++)n[p-1]=arguments[p];return i.some((function(i){return!$(a)&&i&&i.apply(void 0,[a].concat(n)),$(a)}))}}function J(){return"showOpenFilePicker"in window}function Q(a){return B(a)?[{description:"Files",accept:Object.entries(a).filter((function(a){var i=d(a,2),t=i[0],n=i[1],p=!0;return aa(t)||(console.warn('Skipped "'.concat(t,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),p=!1),Array.isArray(n)&&n.every(ia)||(console.warn('Skipped "'.concat(t,'" because an invalid file extension was provided.')),p=!1),p})).reduce((function(a,i){var t=d(i,2),n=t[0],p=t[1];return c(c({},a),{},l({},n,p))}),{})}]:a}function V(a){if(B(a))return Object.entries(a).reduce((function(a,i){var t=d(i,2),n=t[0],p=t[1];return[].concat(s(a),[n],s(p))}),[]).filter((function(a){return aa(a)||ia(a)})).join(",")}function X(a){return a instanceof DOMException&&("AbortError"===a.name||a.code===a.ABORT_ERR)}function Z(a){return a instanceof DOMException&&("SecurityError"===a.name||a.code===a.SECURITY_ERR)}function aa(a){return"audio/*"===a||"video/*"===a||"image/*"===a||"text/*"===a||"application/*"===a||/\w+\/[-+.\w]+/g.test(a)}function ia(a){return/^.*\.[\w]+$/.test(a)}var ta=["children"],na=["open"],pa=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],ea=["refKey","onChange","onClick"],oa=i.forwardRef((function(a,t){var n=a.children,e=ra(r(a,ta)),o=e.open,l=r(e,na);return i.useImperativeHandle(t,(function(){return{open:o}}),[o]),p.default.createElement(i.Fragment,null,n(c(c({},l),{},{open:o})))}));oa.displayName="Dropzone";var ca={disabled:!1,getFilesFromEvent:function(a){return x(this,void 0,void 0,(function*(){return h(a)&&h(a.dataTransfer)?function(a,i){return x(this,void 0,void 0,(function*(){if(a.items){const t=y(a.items).filter((a=>"file"===a.kind));if("drop"!==i)return t;return w(j(yield Promise.all(t.map(k))))}return w(y(a.files).map((a=>f(a))))}))}(a.dataTransfer,a.type):function(a){return h(a)&&h(a.target)}(a)?function(a){return y(a.target.files).map((a=>f(a)))}(a):Array.isArray(a)&&a.every((a=>"getFile"in a&&"function"==typeof a.getFile))?function(a){return x(this,void 0,void 0,(function*(){return(yield Promise.all(a.map((a=>a.getFile())))).map((a=>f(a)))}))}(a):[]}))},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};oa.defaultProps=ca,oa.propTypes={children:e.default.func,accept:e.default.objectOf(e.default.arrayOf(e.default.string)),multiple:e.default.bool,preventDropOnDocument:e.default.bool,noClick:e.default.bool,noKeyboard:e.default.bool,noDrag:e.default.bool,noDragEventsBubbling:e.default.bool,minSize:e.default.number,maxSize:e.default.number,maxFiles:e.default.number,disabled:e.default.bool,getFilesFromEvent:e.default.func,onFileDialogCancel:e.default.func,onFileDialogOpen:e.default.func,useFsAccessApi:e.default.bool,autoFocus:e.default.bool,onDragEnter:e.default.func,onDragLeave:e.default.func,onDragOver:e.default.func,onDrop:e.default.func,onDropAccepted:e.default.func,onDropRejected:e.default.func,onError:e.default.func,validator:e.default.func};var la={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function ra(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=c(c({},ca),a),n=t.accept,p=t.disabled,e=t.getFilesFromEvent,o=t.maxSize,m=t.minSize,v=t.multiple,x=t.maxFiles,u=t.onDragEnter,f=t.onDragLeave,g=t.onDragOver,b=t.onDrop,h=t.onDropAccepted,w=t.onDropRejected,y=t.onFileDialogCancel,k=t.onFileDialogOpen,j=t.useFsAccessApi,z=t.autoFocus,D=t.preventDropOnDocument,F=t.noClick,q=t.noKeyboard,E=t.noDrag,O=t.noDragEventsBubbling,A=t.onError,P=t.validator,S=i.useMemo((function(){return V(n)}),[n]),C=i.useMemo((function(){return Q(n)}),[n]),R=i.useMemo((function(){return"function"==typeof k?k:sa}),[k]),T=i.useMemo((function(){return"function"==typeof y?y:sa}),[y]),I=i.useRef(null),B=i.useRef(null),W=i.useReducer(da,la),G=d(W,2),aa=G[0],ia=G[1],ta=aa.isFocused,na=aa.isFileDialogActive,oa=i.useRef("undefined"!=typeof window&&window.isSecureContext&&j&&J()),ra=function(){!oa.current&&na&&setTimeout((function(){B.current&&(B.current.files.length||(ia({type:"closeDialog"}),T()))}),300)};i.useEffect((function(){return window.addEventListener("focus",ra,!1),function(){window.removeEventListener("focus",ra,!1)}}),[B,na,T,oa]);var ma=i.useRef([]),va=function(a){I.current&&I.current.contains(a.target)||(a.preventDefault(),ma.current=[])};i.useEffect((function(){return D&&(document.addEventListener("dragover",U,!1),document.addEventListener("drop",va,!1)),function(){D&&(document.removeEventListener("dragover",U),document.removeEventListener("drop",va))}}),[I,D]),i.useEffect((function(){return!p&&z&&I.current&&I.current.focus(),function(){}}),[I,z,p]);var xa=i.useCallback((function(a){A?A(a):console.error(a)}),[A]),ua=i.useCallback((function(a){a.preventDefault(),a.persist(),Ea(a),ma.current=[].concat(s(ma.current),[a.target]),H(a)&&Promise.resolve(e(a)).then((function(i){if(!$(a)||O){var t=i.length,n=t>0&&K({files:i,accept:S,minSize:m,maxSize:o,multiple:v,maxFiles:x,validator:P});ia({isDragAccept:n,isDragReject:t>0&&!n,isDragActive:!0,type:"setDraggedFiles"}),u&&u(a)}})).catch((function(a){return xa(a)}))}),[e,u,xa,O,S,m,o,v,x,P]),fa=i.useCallback((function(a){a.preventDefault(),a.persist(),Ea(a);var i=H(a);if(i&&a.dataTransfer)try{a.dataTransfer.dropEffect="copy"}catch(a){}return i&&g&&g(a),!1}),[g,O]),ga=i.useCallback((function(a){a.preventDefault(),a.persist(),Ea(a);var i=ma.current.filter((function(a){return I.current&&I.current.contains(a)})),t=i.indexOf(a.target);-1!==t&&i.splice(t,1),ma.current=i,i.length>0||(ia({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),H(a)&&f&&f(a))}),[I,f,O]),ba=i.useCallback((function(a,i){var t=[],n=[];a.forEach((function(a){var i=d(L(a,S),2),p=i[0],e=i[1],c=d(_(a,m,o),2),l=c[0],r=c[1],s=P?P(a):null;if(p&&l&&!s)t.push(a);else{var v=[e,r];s&&(v=v.concat(s)),n.push({file:a,errors:v.filter((function(a){return a}))})}})),(!v&&t.length>1||v&&x>=1&&t.length>x)&&(t.forEach((function(a){n.push({file:a,errors:[M]})})),t.splice(0)),ia({acceptedFiles:t,fileRejections:n,isDragReject:n.length>0,type:"setFiles"}),b&&b(t,n,i),n.length>0&&w&&w(n,i),t.length>0&&h&&h(t,i)}),[ia,v,S,m,o,x,b,h,w,P]),ha=i.useCallback((function(a){a.preventDefault(),a.persist(),Ea(a),ma.current=[],H(a)&&Promise.resolve(e(a)).then((function(i){$(a)&&!O||ba(i,a)})).catch((function(a){return xa(a)})),ia({type:"reset"})}),[e,ba,xa,O]),wa=i.useCallback((function(){if(oa.current){ia({type:"openDialog"}),R();var a={multiple:v,types:C};window.showOpenFilePicker(a).then((function(a){return e(a)})).then((function(a){ba(a,null),ia({type:"closeDialog"})})).catch((function(a){X(a)?(T(a),ia({type:"closeDialog"})):Z(a)?(oa.current=!1,B.current?(B.current.value=null,B.current.click()):xa(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):xa(a)}))}else B.current&&(ia({type:"openDialog"}),R(),B.current.value=null,B.current.click())}),[ia,R,T,j,ba,xa,C,v]),ya=i.useCallback((function(a){I.current&&I.current.isEqualNode(a.target)&&(" "!==a.key&&"Enter"!==a.key&&32!==a.keyCode&&13!==a.keyCode||(a.preventDefault(),wa()))}),[I,wa]),ka=i.useCallback((function(){ia({type:"focus"})}),[]),ja=i.useCallback((function(){ia({type:"blur"})}),[]),za=i.useCallback((function(){F||(N()?setTimeout(wa,0):wa())}),[F,wa]),Da=function(a){return p?null:a},Fa=function(a){return q?null:Da(a)},qa=function(a){return E?null:Da(a)},Ea=function(a){O&&a.stopPropagation()},Oa=i.useMemo((function(){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=a.refKey,t=void 0===i?"ref":i,n=a.role,e=a.onKeyDown,o=a.onFocus,d=a.onBlur,s=a.onClick,m=a.onDragEnter,v=a.onDragOver,x=a.onDragLeave,u=a.onDrop,f=r(a,pa);return c(c(l({onKeyDown:Fa(Y(e,ya)),onFocus:Fa(Y(o,ka)),onBlur:Fa(Y(d,ja)),onClick:Da(Y(s,za)),onDragEnter:qa(Y(m,ua)),onDragOver:qa(Y(v,fa)),onDragLeave:qa(Y(x,ga)),onDrop:qa(Y(u,ha)),role:"string"==typeof n&&""!==n?n:"presentation"},t,I),p||q?{}:{tabIndex:0}),f)}}),[I,ya,ka,ja,za,ua,fa,ga,ha,q,E,p]),Aa=i.useCallback((function(a){a.stopPropagation()}),[]),Pa=i.useMemo((function(){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=a.refKey,t=void 0===i?"ref":i,n=a.onChange,p=a.onClick,e=r(a,ea),o=l({accept:S,multiple:v,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Da(Y(n,ha)),onClick:Da(Y(p,Aa)),tabIndex:-1},t,B);return c(c({},o),e)}}),[B,n,v,ha,p]);return c(c({},aa),{},{isFocused:ta&&!p,getRootProps:Oa,getInputProps:Pa,rootRef:I,inputRef:B,open:Da(wa)})}function da(a,i){switch(i.type){case"focus":return c(c({},a),{},{isFocused:!0});case"blur":return c(c({},a),{},{isFocused:!1});case"openDialog":return c(c({},la),{},{isFileDialogActive:!0});case"closeDialog":return c(c({},a),{},{isFileDialogActive:!1});case"setDraggedFiles":return c(c({},a),{},{isDragActive:i.isDragActive,isDragAccept:i.isDragAccept,isDragReject:i.isDragReject});case"setFiles":return c(c({},a),{},{acceptedFiles:i.acceptedFiles,fileRejections:i.fileRejections,isDragReject:i.isDragReject});case"reset":return c({},la);default:return a}}function sa(){}a.ErrorCode=C,a.default=oa,a.useDropzone=ra,Object.defineProperty(a,"__esModule",{value:!0})}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
