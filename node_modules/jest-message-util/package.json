{"name": "jest-message-util", "version": "30.0.2", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-message-util"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/code-frame": "^7.27.1", "@jest/types": "30.0.1", "@types/stack-utils": "^2.0.3", "chalk": "^4.1.2", "graceful-fs": "^4.2.11", "micromatch": "^4.0.8", "pretty-format": "30.0.2", "slash": "^3.0.0", "stack-utils": "^2.0.6"}, "devDependencies": {"@types/babel__code-frame": "^7.0.6", "@types/graceful-fs": "^4.1.9", "@types/micromatch": "^4.0.9", "tempy": "^1.0.1"}, "publishConfig": {"access": "public"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c"}