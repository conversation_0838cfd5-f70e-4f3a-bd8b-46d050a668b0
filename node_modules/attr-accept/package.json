{"name": "attr-accept", "description": "JavaScript implementation of the \"accept\" attribute for HTML5 <input type=\"file\">", "version": "2.2.5", "main": "dist/index.js", "module": "dist/es/index.js", "types": "index.d.ts", "files": ["dist", "index.d.ts"], "scripts": {"clean": "rimraf ./dist", "build": "npm run clean && npm run build:cjs && npm run build:es", "build:cjs": "webpack -p", "build:es": "cross-env BABEL_ENV=es babel ./src --out-dir ./dist/es --ignore '**/*.spec.js'", "prepublish": "npm test && npm run build", "pretest": "npm run lint", "test": "mocha --require @babel/register --recursive", "test:watch": "npm test -- --watch", "test:cov": "nyc --reporter=lcov --reporter=text mocha --require @babel/register --recursive", "lint": "eslint src/ test/", "precommit": "lint-staged", "size": "size-limit", "size:why": "size-limit --why"}, "repository": {"type": "git", "url": "https://github.com/react-dropzone/attr-accept.git"}, "publishConfig": {"access": "public"}, "keywords": ["html5", "input", "tag", "attribute", "attr", "accept", "file"], "author": "<PERSON><PERSON> @okonetchnikov", "contributors": ["<PERSON><PERSON> <<EMAIL>> (http://okonet.ru)", "<PERSON> <<EMAIL>>"], "license": "MIT", "bugs": {"url": "https://github.com/react-dropzone/attr-accept/issues"}, "homepage": "https://github.com/react-dropzone/attr-accept#readme", "devDependencies": {"@babel/cli": "^7.6.4", "@babel/core": "^7.10.5", "@babel/plugin-external-helpers": "^7.2.0", "@babel/plugin-proposal-do-expressions": "^7.6.0", "@babel/plugin-proposal-export-default-from": "^7.5.2", "@babel/plugin-proposal-logical-assignment-operators": "^7.2.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.4.4", "@babel/plugin-proposal-optional-chaining": "^7.6.0", "@babel/plugin-proposal-pipeline-operator": "^7.5.0", "@babel/plugin-transform-runtime": "^7.6.2", "@babel/preset-env": "^7.6.3", "@babel/preset-react": "^7.6.3", "@babel/register": "^7.6.2", "@commitlint/cli": "^8.2.0", "@size-limit/preset-small-lib": "^2.1.6", "babel-eslint": "^8.0.0", "babel-loader": "^8.0.0", "babel-preset-env": "^1.6.0", "core-js": "^3.1.4", "cross-env": "^6.0.3", "cz-conventional-changelog": "^1.2.0", "eslint": "^4.6.1", "eslint-config-airbnb": "^15.1.0", "eslint-config-okonet": "^6.1.3", "eslint-config-prettier": "^2.4.0", "eslint-plugin-flowtype": "^2.35.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-node": "^5.1.1", "eslint-plugin-prettier": "^2.2.0", "eslint-plugin-react": "^7.3.0", "expect": "^1.20.2", "husky": "^0.14.3", "lint-staged": "^4.1.3", "mocha": "^9.2.2", "nyc": "^15.1.0", "prettier": "^1.6.1", "rimraf": "^3.0.0", "webpack": "^4.0.0", "webpack-cli": "^3.3.12"}, "engines": {"node": ">=4"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "size-limit": [{"path": "dist/index.js", "limit": "2 KB"}]}