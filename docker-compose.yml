version: '3.8'

services:
  # CVmatic Next.js Frontend Application
  cvmatic-web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - CV_ANALYSIS_API_URL=http://cv-analysis:8000
    networks:
      - cvmatic-network
    restart: unless-stopped
    depends_on:
      - cv-analysis
      - postgres
      - redis
    healthcheck:
      test: [ "CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 3

  # CV Analysis Microservice
  cv-analysis:
    build:
      context: ./cv-analysis-service
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************************/cvmatic_db
      - REDIS_URL=redis://redis:6379/0
      - OLLAMA_BASE_URL=http://ollama:11434
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
    volumes:
      - cv_uploads:/app/uploads
      - cv_models:/app/models
    networks:
      - cvmatic-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
      - ollama
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/health" ]
      interval: 30s
      timeout: 10s
      retries: 3

  # Ollama for Local LLM
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - cvmatic-network
    restart: unless-stopped
    environment:
      - OLLAMA_ORIGINS=*
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:11434/api/tags" ]
      interval: 60s
      timeout: 30s
      retries: 5

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=cvmatic_db
      - POSTGRES_USER=cvmatic_user
      - POSTGRES_PASSWORD=cvmatic_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./cv-analysis-service/scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - cvmatic-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U cvmatic_user -d cvmatic_db" ]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cvmatic-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./cv-analysis-service/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - cvmatic-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./cv-analysis-service/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./cv-analysis-service/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - cvmatic-network
    restart: unless-stopped
    depends_on:
      - prometheus

networks:
  cvmatic-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  cv_uploads:
    driver: local
  cv_models:
    driver: local
  ollama_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
