version: '3.8'

services:
  # CVmatic Web Application
  cvmatic-web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    volumes:
      # For development, mount source code
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - cvmatic-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # CVmatic Python Microservice (placeholder for future implementation)
  cvmatic-parser:
    image: python:3.11-alpine
    command: ["sh", "-c", "echo 'CVmatic Parser Service - Coming Soon' && sleep infinity"]
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - FLASK_ENV=production
    volumes:
      - ./parser:/app
    networks:
      - cvmatic-network
    restart: unless-stopped
    depends_on:
      - cvmatic-web

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cvmatic-network
    restart: unless-stopped
    command: redis-server --appendonly yes

networks:
  cvmatic-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
