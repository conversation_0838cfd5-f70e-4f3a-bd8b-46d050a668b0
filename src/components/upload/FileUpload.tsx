'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  FileText, 
  X, 
  CheckCircle, 
  AlertCircle,
  Loader2 
} from 'lucide-react';

interface FileUploadProps {
  onFileUpload?: (file: File) => Promise<void>;
  isRTL?: boolean;
  maxSize?: number; // in bytes
  acceptedFormats?: string[];
}

interface UploadedFile {
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

export default function FileUpload({ 
  onFileUpload,
  isRTL = false,
  maxSize = 4 * 1024 * 1024, // 4MB
  acceptedFormats = ['.pdf', '.doc', '.docx']
}: FileUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragActive, setIsDragActive] = useState(false);

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxSize) {
      return isRTL 
        ? `גודל הקובץ חורג מ-${Math.round(maxSize / (1024 * 1024))}MB`
        : `File size exceeds ${Math.round(maxSize / (1024 * 1024))}MB limit`;
    }

    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!acceptedFormats.includes(fileExtension)) {
      return isRTL
        ? `פורמט קובץ לא נתמך. פורמטים מותרים: ${acceptedFormats.join(', ')}`
        : `Unsupported file format. Allowed formats: ${acceptedFormats.join(', ')}`;
    }

    return null;
  };

  const simulateUpload = async (file: File): Promise<void> => {
    return new Promise((resolve, reject) => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          
          // Simulate random success/failure for demo
          if (Math.random() > 0.1) { // 90% success rate
            resolve();
          } else {
            reject(new Error(isRTL ? 'שגיאה בהעלאת הקובץ' : 'Upload failed'));
          }
        }
        
        setUploadedFiles(prev => 
          prev.map(f => 
            f.file === file 
              ? { ...f, progress }
              : f
          )
        );
      }, 200);
    });
  };

  const handleFileUpload = async (file: File) => {
    const error = validateFile(file);
    if (error) {
      setUploadedFiles(prev => [...prev, {
        file,
        progress: 0,
        status: 'error',
        error
      }]);
      return;
    }

    // Add file to upload queue
    setUploadedFiles(prev => [...prev, {
      file,
      progress: 0,
      status: 'uploading'
    }]);

    try {
      if (onFileUpload) {
        await onFileUpload(file);
      } else {
        await simulateUpload(file);
      }
      
      setUploadedFiles(prev => 
        prev.map(f => 
          f.file === file 
            ? { ...f, status: 'success', progress: 100 }
            : f
        )
      );
    } catch (err) {
      setUploadedFiles(prev => 
        prev.map(f => 
          f.file === file 
            ? { 
                ...f, 
                status: 'error', 
                error: err instanceof Error ? err.message : 'Upload failed'
              }
            : f
        )
      );
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(handleFileUpload);
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive: dropzoneActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxSize,
    multiple: false,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    onDropAccepted: () => setIsDragActive(false),
    onDropRejected: () => setIsDragActive(false)
  });

  const removeFile = (fileToRemove: File) => {
    setUploadedFiles(prev => prev.filter(f => f.file !== fileToRemove));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="w-full max-w-2xl mx-auto space-y-6">
      {/* Upload Area */}
      <Card className="border-2 border-dashed transition-colors duration-200 hover:border-primary/50">
        <CardContent className="p-8">
          <div
            {...getRootProps()}
            className={`
              text-center cursor-pointer transition-all duration-200
              ${(isDragActive || dropzoneActive) ? 'scale-105' : ''}
            `}
          >
            <input {...getInputProps()} />
            
            <div className="flex flex-col items-center space-y-4">
              <div className={`
                p-4 rounded-full transition-colors duration-200
                ${(isDragActive || dropzoneActive) 
                  ? 'bg-primary/10 text-primary' 
                  : 'bg-muted text-muted-foreground'
                }
              `}>
                <Upload className="h-8 w-8" />
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">
                  {isRTL ? 'העלה את קובץ הקו״ח שלך' : 'Upload Your Resume'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {isRTL 
                    ? 'גרור ושחרר או לחץ לבחירת קובץ'
                    : 'Drag and drop or click to select a file'
                  }
                </p>
                <p className="text-xs text-muted-foreground">
                  {isRTL
                    ? `פורמטים נתמכים: ${acceptedFormats.join(', ')} • מקסימום ${Math.round(maxSize / (1024 * 1024))}MB`
                    : `Supported formats: ${acceptedFormats.join(', ')} • Max ${Math.round(maxSize / (1024 * 1024))}MB`
                  }
                </p>
              </div>
              
              <Button variant="outline" className="mt-4">
                {isRTL ? 'בחר קובץ' : 'Choose File'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium">
            {isRTL ? 'קבצים שהועלו' : 'Uploaded Files'}
          </h4>
          
          {uploadedFiles.map((uploadedFile, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-primary flex-shrink-0" />
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium truncate">
                      {uploadedFile.file.name}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(uploadedFile.file)}
                      className="flex-shrink-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(uploadedFile.file.size)}
                  </p>
                  
                  {uploadedFile.status === 'uploading' && (
                    <div className="mt-2 space-y-1">
                      <Progress value={uploadedFile.progress} className="h-2" />
                      <div className="flex items-center space-x-2">
                        <Loader2 className="h-3 w-3 animate-spin" />
                        <span className="text-xs text-muted-foreground">
                          {isRTL ? 'מעלה...' : 'Uploading...'}
                        </span>
                      </div>
                    </div>
                  )}
                  
                  {uploadedFile.status === 'success' && (
                    <div className="mt-2 flex items-center space-x-2 text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-xs">
                        {isRTL ? 'הועלה בהצלחה' : 'Upload successful'}
                      </span>
                    </div>
                  )}
                  
                  {uploadedFile.status === 'error' && (
                    <Alert className="mt-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-xs">
                        {uploadedFile.error}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
