'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  FileText,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  Shield,
  AlertTriangle,
  Brain
} from 'lucide-react';
import { validateFile, sanitizeFileName, UploadRateLimit } from '@/lib/security';

interface FileUploadProps {
  onFileUpload?: (file: File) => Promise<void>;
  onAnalysisComplete?: (result: any) => void;
  isRTL?: boolean;
  maxSize?: number; // in bytes
  acceptedFormats?: string[];
  useLLM?: boolean;
  analysisType?: 'skills_extraction' | 'experience_analysis' | 'content_quality' | 'job_matching';
  targetPosition?: string;
  targetIndustry?: string;
}

interface UploadedFile {
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error' | 'validating' | 'analyzing';
  error?: string;
  warnings?: string[];
  sanitizedName?: string;
  analysisResult?: any;
}

export default function FileUpload({
  onFileUpload,
  onAnalysisComplete,
  isRTL = false,
  maxSize = 4 * 1024 * 1024, // 4MB
  acceptedFormats = ['.pdf', '.doc', '.docx'],
  useLLM = false,
  analysisType = 'skills_extraction',
  targetPosition,
  targetIndustry
}: FileUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragActive, setIsDragActive] = useState(false);
  const [rateLimit] = useState(() => new UploadRateLimit());

  const getClientIdentifier = (): string => {
    // In a real app, you'd use IP address or user ID
    // For demo purposes, we'll use a browser fingerprint
    return 'demo-client-' + (typeof window !== 'undefined' ? window.navigator.userAgent.slice(0, 20) : 'server');
  };

  const uploadAndAnalyzeFile = async (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);

    // Add optional parameters
    if (targetPosition) formData.append('target_position', targetPosition);
    if (targetIndustry) formData.append('target_industry', targetIndustry);
    if (useLLM) formData.append('analysis_type', analysisType);
    formData.append('language', 'auto');

    // Choose endpoint based on LLM usage
    const endpoint = useLLM ? '/api/analyze-llm' : '/api/analyze';

    // Update status to analyzing
    setUploadedFiles(prev =>
      prev.map(f =>
        f.file === file
          ? { ...f, status: 'analyzing', progress: 50 }
          : f
      )
    );

    const response = await fetch(endpoint, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Analysis failed');
    }

    const result = await response.json();

    // Update progress to complete
    setUploadedFiles(prev =>
      prev.map(f =>
        f.file === file
          ? { ...f, progress: 100 }
          : f
      )
    );

    return result.data;
  };

  const handleFileUpload = async (file: File) => {
    const clientId = getClientIdentifier();

    // Check rate limiting
    if (!rateLimit.isAllowed(clientId)) {
      const resetTime = new Date(rateLimit.getResetTime(clientId));
      const error = isRTL
        ? `חרגת ממגבלת ההעלאות. נסה שוב ב-${resetTime.toLocaleTimeString()}`
        : `Upload limit exceeded. Try again at ${resetTime.toLocaleTimeString()}`;

      setUploadedFiles(prev => [...prev, {
        file,
        progress: 0,
        status: 'error',
        error
      }]);
      return;
    }

    // Record the attempt
    rateLimit.recordAttempt(clientId);

    // Add file to validation queue
    setUploadedFiles(prev => [...prev, {
      file,
      progress: 0,
      status: 'validating'
    }]);

    try {
      // Validate file with security checks
      const validation = await validateFile(file);

      if (!validation.isValid) {
        setUploadedFiles(prev =>
          prev.map(f =>
            f.file === file
              ? {
                ...f,
                status: 'error',
                error: validation.errors.join(', ')
              }
              : f
          )
        );
        return;
      }

      // Sanitize filename
      const sanitizedName = sanitizeFileName(file.name);

      // Update to uploading status with warnings if any
      setUploadedFiles(prev =>
        prev.map(f =>
          f.file === file
            ? {
              ...f,
              status: 'uploading',
              warnings: validation.warnings,
              sanitizedName
            }
            : f
        )
      );

      // Proceed with upload and analysis
      let analysisResult: any = null;
      if (onFileUpload) {
        await onFileUpload(file);
        // If custom upload handler is provided, still mark as success
        setUploadedFiles(prev =>
          prev.map(f =>
            f.file === file
              ? { ...f, status: 'success', progress: 100 }
              : f
          )
        );
      } else {
        // Use our API to upload and analyze
        analysisResult = await uploadAndAnalyzeFile(file);

        setUploadedFiles(prev =>
          prev.map(f =>
            f.file === file
              ? {
                ...f,
                status: 'success',
                progress: 100,
                analysisResult
              }
              : f
          )
        );

        // Call the analysis complete callback if provided
        if (onAnalysisComplete && analysisResult) {
          onAnalysisComplete(analysisResult);
        }
      }
    } catch (err) {
      setUploadedFiles(prev =>
        prev.map(f =>
          f.file === file
            ? {
              ...f,
              status: 'error',
              error: err instanceof Error ? err.message : 'Upload failed'
            }
            : f
        )
      );
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(handleFileUpload);
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive: dropzoneActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxSize,
    multiple: false,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    onDropAccepted: () => setIsDragActive(false),
    onDropRejected: () => setIsDragActive(false)
  });

  const removeFile = (fileToRemove: File) => {
    setUploadedFiles(prev => prev.filter(f => f.file !== fileToRemove));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="w-full max-w-2xl mx-auto space-y-6">
      {/* Upload Area */}
      <Card className="border-2 border-dashed transition-colors duration-200 hover:border-primary/50">
        <CardContent className="p-8">
          <div
            {...getRootProps()}
            className={`
              text-center cursor-pointer transition-all duration-200
              ${(isDragActive || dropzoneActive) ? 'scale-105' : ''}
            `}
          >
            <input {...getInputProps()} />

            <div className="flex flex-col items-center space-y-4">
              <div className={`
                p-4 rounded-full transition-colors duration-200
                ${(isDragActive || dropzoneActive)
                  ? 'bg-primary/10 text-primary'
                  : 'bg-muted text-muted-foreground'
                }
              `}>
                <Upload className="h-8 w-8" />
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-semibold">
                  {isRTL ? 'העלה את קובץ הקו״ח שלך' : 'Upload Your Resume'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {isRTL
                    ? 'גרור ושחרר או לחץ לבחירת קובץ'
                    : 'Drag and drop or click to select a file'
                  }
                </p>
                <p className="text-xs text-muted-foreground">
                  {isRTL
                    ? `פורמטים נתמכים: ${acceptedFormats.join(', ')} • מקסימום ${Math.round(maxSize / (1024 * 1024))}MB`
                    : `Supported formats: ${acceptedFormats.join(', ')} • Max ${Math.round(maxSize / (1024 * 1024))}MB`
                  }
                </p>
              </div>

              <Button variant="outline" className="mt-4">
                {isRTL ? 'בחר קובץ' : 'Choose File'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium">
            {isRTL ? 'קבצים שהועלו' : 'Uploaded Files'}
          </h4>

          {uploadedFiles.map((uploadedFile, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-primary flex-shrink-0" />

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {uploadedFile.file.name}
                      </p>
                      {uploadedFile.sanitizedName && uploadedFile.sanitizedName !== uploadedFile.file.name && (
                        <p className="text-xs text-muted-foreground">
                          {isRTL ? 'שם מנוקה:' : 'Sanitized:'} {uploadedFile.sanitizedName}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {uploadedFile.status === 'validating' && (
                        <Badge variant="outline" className="text-xs">
                          <Shield className="h-3 w-3 mr-1" />
                          {isRTL ? 'בודק' : 'Validating'}
                        </Badge>
                      )}
                      {uploadedFile.status === 'analyzing' && (
                        <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                          <Brain className="h-3 w-3 mr-1 animate-pulse" />
                          {isRTL ? 'מנתח' : 'Analyzing'}
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(uploadedFile.file)}
                        className="flex-shrink-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(uploadedFile.file.size)}
                  </p>

                  {uploadedFile.status === 'validating' && (
                    <div className="mt-2 flex items-center space-x-2 text-blue-600">
                      <Shield className="h-4 w-4 animate-pulse" />
                      <span className="text-xs">
                        {isRTL ? 'בודק אבטחה...' : 'Security validation...'}
                      </span>
                    </div>
                  )}

                  {(uploadedFile.status === 'uploading' || uploadedFile.status === 'analyzing') && (
                    <div className="mt-2 space-y-1">
                      <Progress value={uploadedFile.progress} className="h-2" />
                      <div className="flex items-center space-x-2">
                        <Loader2 className="h-3 w-3 animate-spin" />
                        <span className="text-xs text-muted-foreground">
                          {uploadedFile.status === 'analyzing'
                            ? (isRTL ? 'מנתח עם AI...' : 'Analyzing with AI...')
                            : (isRTL ? 'מעלה...' : 'Uploading...')
                          }
                        </span>
                      </div>
                    </div>
                  )}

                  {uploadedFile.status === 'success' && (
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center space-x-2 text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        <span className="text-xs">
                          {isRTL ? 'הועלה בהצלחה' : 'Upload successful'}
                        </span>
                      </div>

                      {uploadedFile.warnings && uploadedFile.warnings.length > 0 && (
                        <Alert className="border-yellow-200 bg-yellow-50">
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                          <AlertDescription className="text-xs">
                            <div className="font-medium mb-1">
                              {isRTL ? 'אזהרות אבטחה:' : 'Security warnings:'}
                            </div>
                            <ul className="list-disc list-inside space-y-1">
                              {uploadedFile.warnings.map((warning, idx) => (
                                <li key={idx}>{warning}</li>
                              ))}
                            </ul>
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}

                  {uploadedFile.status === 'error' && (
                    <Alert className="mt-2 border-red-200 bg-red-50">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <AlertDescription className="text-xs">
                        {uploadedFile.error}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
