import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import FileUpload from '../FileUpload'

// Mock the security module
jest.mock('@/lib/security', () => ({
  validateFile: jest.fn(),
  sanitizeFileName: jest.fn(),
  UploadRateLimit: jest.fn().mockImplementation(() => ({
    isAllowed: jest.fn(() => true),
    recordAttempt: jest.fn(),
    getRemainingAttempts: jest.fn(() => 5),
    getResetTime: jest.fn(() => Date.now() + 60000)
  }))
}))

// Mock react-dropzone
jest.mock('react-dropzone', () => ({
  useDropzone: jest.fn(({ onDrop }) => ({
    getRootProps: () => ({
      onClick: jest.fn()
    }),
    getInputProps: () => ({
      type: 'file'
    }),
    isDragActive: false
  }))
}))

const mockValidateFile = require('@/lib/security').validateFile
const mockSanitizeFileName = require('@/lib/security').sanitizeFileName

describe('FileUpload Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockValidateFile.mockResolvedValue({
      isValid: true,
      errors: [],
      warnings: [],
      fileInfo: {
        name: 'test.pdf',
        size: 1024,
        type: 'application/pdf',
        extension: '.pdf'
      }
    })
    mockSanitizeFileName.mockReturnValue('test.pdf')
  })

  it('renders upload area correctly', () => {
    render(<FileUpload />)

    expect(screen.getByText('Upload Your Resume')).toBeInTheDocument()
    expect(screen.getByText('Drag and drop or click to select a file')).toBeInTheDocument()
    expect(screen.getByText('Choose File')).toBeInTheDocument()
  })

  it('renders in RTL mode correctly', () => {
    render(<FileUpload isRTL={true} />)

    expect(screen.getByText('העלה את קובץ הקו״ח שלך')).toBeInTheDocument()
    expect(screen.getByText('גרור ושחרר או לחץ לבחירת קובץ')).toBeInTheDocument()
    expect(screen.getByText('בחר קובץ')).toBeInTheDocument()
  })

  it('displays file format and size requirements', () => {
    render(<FileUpload />)

    expect(screen.getByText(/Supported formats: \.pdf, \.doc, \.docx/)).toBeInTheDocument()
    expect(screen.getByText(/Max 4MB/)).toBeInTheDocument()
  })

  it('handles successful file upload', async () => {
    const mockOnFileUpload = jest.fn().mockResolvedValue(undefined)
    render(<FileUpload onFileUpload={mockOnFileUpload} />)

    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })

    // Simulate file drop
    const { useDropzone } = require('react-dropzone')
    const onDrop = useDropzone.mock.calls[0][0].onDrop

    await waitFor(() => {
      onDrop([file])
    })

    await waitFor(() => {
      expect(mockValidateFile).toHaveBeenCalledWith(file)
      expect(mockSanitizeFileName).toHaveBeenCalledWith('test.pdf')
      expect(mockOnFileUpload).toHaveBeenCalledWith(file)
    })
  })

  it('handles file validation errors', async () => {
    mockValidateFile.mockResolvedValue({
      isValid: false,
      errors: ['File size exceeds maximum limit'],
      warnings: [],
      fileInfo: {
        name: 'large.pdf',
        size: 5 * 1024 * 1024,
        type: 'application/pdf',
        extension: '.pdf'
      }
    })

    render(<FileUpload />)

    const file = new File(['test content'], 'large.pdf', { type: 'application/pdf' })

    const { useDropzone } = require('react-dropzone')
    const onDrop = useDropzone.mock.calls[0][0].onDrop

    await waitFor(() => {
      onDrop([file])
    })

    await waitFor(() => {
      expect(screen.getByText('File size exceeds maximum limit')).toBeInTheDocument()
    })
  })

  it('displays security warnings for valid files', async () => {
    mockValidateFile.mockResolvedValue({
      isValid: true,
      errors: [],
      warnings: ['PDF contains embedded archive - please verify content'],
      fileInfo: {
        name: 'suspicious.pdf',
        size: 1024,
        type: 'application/pdf',
        extension: '.pdf'
      }
    })

    const mockOnFileUpload = jest.fn().mockResolvedValue(undefined)
    render(<FileUpload onFileUpload={mockOnFileUpload} />)

    const file = new File(['test content'], 'suspicious.pdf', { type: 'application/pdf' })

    const { useDropzone } = require('react-dropzone')
    const onDrop = useDropzone.mock.calls[0][0].onDrop

    await waitFor(() => {
      onDrop([file])
    })

    await waitFor(() => {
      expect(screen.getByText('Security warnings:')).toBeInTheDocument()
      expect(screen.getByText('PDF contains embedded archive - please verify content')).toBeInTheDocument()
    })
  })

  it('handles rate limiting', async () => {
    // Create a new mock for this specific test
    const mockRateLimitInstance = {
      isAllowed: jest.fn(() => false),
      recordAttempt: jest.fn(),
      getRemainingAttempts: jest.fn(() => 0),
      getResetTime: jest.fn(() => Date.now() + 60000)
    }

    const mockRateLimit = require('@/lib/security').UploadRateLimit
    mockRateLimit.mockImplementation(() => mockRateLimitInstance)

    render(<FileUpload />)

    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })

    const { useDropzone } = require('react-dropzone')
    const onDrop = useDropzone.mock.calls[0][0].onDrop

    await waitFor(() => {
      onDrop([file])
    })

    await waitFor(() => {
      expect(screen.getByText(/Upload limit exceeded/)).toBeInTheDocument()
    })
  })

  it('shows sanitized filename when different from original', async () => {
    mockSanitizeFileName.mockReturnValue('safe_filename.pdf')

    // Reset rate limit mock to allow uploads
    const mockRateLimitInstance = {
      isAllowed: jest.fn(() => true),
      recordAttempt: jest.fn(),
      getRemainingAttempts: jest.fn(() => 5),
      getResetTime: jest.fn(() => Date.now() + 60000)
    }

    const mockRateLimit = require('@/lib/security').UploadRateLimit
    mockRateLimit.mockImplementation(() => mockRateLimitInstance)

    const mockOnFileUpload = jest.fn().mockResolvedValue(undefined)
    render(<FileUpload onFileUpload={mockOnFileUpload} />)

    const file = new File(['test content'], 'unsafe<>filename.pdf', { type: 'application/pdf' })

    const { useDropzone } = require('react-dropzone')
    const onDrop = useDropzone.mock.calls[0][0].onDrop

    await waitFor(() => {
      onDrop([file])
    })

    await waitFor(() => {
      expect(screen.getByText('unsafe<>filename.pdf')).toBeInTheDocument()
      expect(screen.getByText('Sanitized: safe_filename.pdf')).toBeInTheDocument()
    })
  })

  it('allows file removal', async () => {
    const mockOnFileUpload = jest.fn().mockResolvedValue(undefined)
    render(<FileUpload onFileUpload={mockOnFileUpload} />)

    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })

    const { useDropzone } = require('react-dropzone')
    const onDrop = useDropzone.mock.calls[0][0].onDrop

    await waitFor(() => {
      onDrop([file])
    })

    // Wait for file to appear
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })

    // Find and click remove button
    const removeButton = screen.getByRole('button', { name: '' }) // X button
    fireEvent.click(removeButton)

    // File should be removed
    await waitFor(() => {
      expect(screen.queryByText('test.pdf')).not.toBeInTheDocument()
    })
  })

  it('displays file information correctly', async () => {
    const mockOnFileUpload = jest.fn().mockResolvedValue(undefined)
    render(<FileUpload onFileUpload={mockOnFileUpload} />)

    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })

    const { useDropzone } = require('react-dropzone')
    const onDrop = useDropzone.mock.calls[0][0].onDrop

    await waitFor(() => {
      onDrop([file])
    })

    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
      expect(screen.getByText('12 Bytes')).toBeInTheDocument()
    })
  })

  it('formats file sizes correctly', async () => {
    const mockOnFileUpload = jest.fn().mockResolvedValue(undefined)
    render(<FileUpload onFileUpload={mockOnFileUpload} />)

    // Create a 1MB file
    const file = new File(['x'.repeat(1024 * 1024)], 'test.pdf', { type: 'application/pdf' })
    Object.defineProperty(file, 'size', { value: 1024 * 1024 })

    const { useDropzone } = require('react-dropzone')
    const onDrop = useDropzone.mock.calls[0][0].onDrop

    await waitFor(() => {
      onDrop([file])
    })

    await waitFor(() => {
      expect(screen.getByText('1 MB')).toBeInTheDocument()
    })
  })
})
