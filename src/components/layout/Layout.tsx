'use client';

import React, { useState, useEffect } from 'react';
import Header from './Header';
import Footer from './Footer';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

export default function Layout({ children, className = '' }: LayoutProps) {
  const [isRTL, setIsRTL] = useState(false);
  const [isDark, setIsDark] = useState(false);

  // Initialize theme and language from localStorage
  useEffect(() => {
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('cvmatic-theme');
    const savedLanguage = localStorage.getItem('cvmatic-language');
    
    if (savedTheme === 'dark') {
      setIsDark(true);
      document.documentElement.classList.add('dark');
    } else if (savedTheme === 'light') {
      setIsDark(false);
      document.documentElement.classList.remove('dark');
    } else {
      // Default to system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDark(prefersDark);
      if (prefersDark) {
        document.documentElement.classList.add('dark');
      }
    }

    // Check for saved language preference
    if (savedLanguage === 'he') {
      setIsRTL(true);
      document.documentElement.setAttribute('dir', 'rtl');
      document.documentElement.setAttribute('lang', 'he');
    } else {
      setIsRTL(false);
      document.documentElement.setAttribute('dir', 'ltr');
      document.documentElement.setAttribute('lang', 'en');
    }
  }, []);

  const handleLanguageChange = (lang: 'en' | 'he') => {
    const newIsRTL = lang === 'he';
    setIsRTL(newIsRTL);
    
    // Update document attributes
    document.documentElement.setAttribute('dir', newIsRTL ? 'rtl' : 'ltr');
    document.documentElement.setAttribute('lang', lang);
    
    // Save preference
    localStorage.setItem('cvmatic-language', lang);
  };

  const handleThemeToggle = () => {
    const newIsDark = !isDark;
    setIsDark(newIsDark);
    
    // Update document class
    if (newIsDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    
    // Save preference
    localStorage.setItem('cvmatic-theme', newIsDark ? 'dark' : 'light');
  };

  return (
    <div className={`min-h-screen flex flex-col ${isRTL ? 'rtl' : ''}`}>
      <Header
        isRTL={isRTL}
        isDark={isDark}
        onLanguageChange={handleLanguageChange}
        onThemeToggle={handleThemeToggle}
      />
      
      <main className={`flex-1 ${className}`}>
        {children}
      </main>
      
      <Footer isRTL={isRTL} />
    </div>
  );
}
