'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Github, Twitter, Linkedin, Mail } from 'lucide-react';

interface FooterProps {
  isRTL?: boolean;
}

export default function Footer({ isRTL = false }: FooterProps) {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    product: {
      title: isRTL ? 'מוצר' : 'Product',
      links: [
        { href: '/features', label: isRTL ? 'תכונות' : 'Features' },
        { href: '/pricing', label: isRTL ? 'תמחור' : 'Pricing' },
        { href: '/api', label: isRTL ? 'API' : 'API' },
        { href: '/integrations', label: isRTL ? 'אינטגרציות' : 'Integrations' },
      ],
    },
    company: {
      title: isRTL ? 'חברה' : 'Company',
      links: [
        { href: '/about', label: isRTL ? 'אודות' : 'About' },
        { href: '/careers', label: isRTL ? 'קריירה' : 'Careers' },
        { href: '/blog', label: isRTL ? 'בלוג' : 'Blog' },
        { href: '/contact', label: isRTL ? 'צור קשר' : 'Contact' },
      ],
    },
    support: {
      title: isRTL ? 'תמיכה' : 'Support',
      links: [
        { href: '/help', label: isRTL ? 'עזרה' : 'Help Center' },
        { href: '/docs', label: isRTL ? 'תיעוד' : 'Documentation' },
        { href: '/status', label: isRTL ? 'סטטוס' : 'Status' },
        { href: '/security', label: isRTL ? 'אבטחה' : 'Security' },
      ],
    },
    legal: {
      title: isRTL ? 'משפטי' : 'Legal',
      links: [
        { href: '/privacy', label: isRTL ? 'פרטיות' : 'Privacy Policy' },
        { href: '/terms', label: isRTL ? 'תנאי שימוש' : 'Terms of Service' },
        { href: '/cookies', label: isRTL ? 'עוגיות' : 'Cookie Policy' },
        { href: '/gdpr', label: isRTL ? 'GDPR' : 'GDPR' },
      ],
    },
  };

  const socialLinks = [
    { href: 'https://github.com', icon: Github, label: 'GitHub' },
    { href: 'https://twitter.com', icon: Twitter, label: 'Twitter' },
    { href: 'https://linkedin.com', icon: Linkedin, label: 'LinkedIn' },
    { href: 'mailto:<EMAIL>', icon: Mail, label: 'Email' },
  ];

  return (
    <footer className="bg-muted/30 border-t border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <Image
                  src="/CVmatic.png"
                  alt="CVmatic Logo"
                  width={32}
                  height={32}
                  className="h-8 w-auto"
                />
                <span className="text-lg font-bold text-primary">CVmatic</span>
              </div>
              <p className="text-sm text-muted-foreground mb-4 max-w-sm">
                {isRTL 
                  ? 'פלטפורמה חכמה לניתוח קורות חיים. סורקים, מנתחים, מתקבלים.'
                  : 'Smart resume analysis platform. Scan, analyze, get hired.'
                }
              </p>
              <div className="text-xs text-muted-foreground">
                {isRTL ? 'סורקים. מנתחים. מתקבלים' : 'Scan. Analyze. Get hired.'}
              </div>
            </div>

            {/* Footer Links */}
            {Object.entries(footerLinks).map(([key, section]) => (
              <div key={key}>
                <h3 className="text-sm font-semibold text-foreground mb-3">
                  {section.title}
                </h3>
                <ul className="space-y-2">
                  {section.links.map((link) => (
                    <li key={link.href}>
                      <Link
                        href={link.href}
                        className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-6 border-t border-border">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-sm text-muted-foreground">
              © {currentYear} CVmatic. {isRTL ? 'כל הזכויות שמורות.' : 'All rights reserved.'}
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <Link
                    key={social.href}
                    href={social.href}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                    aria-label={social.label}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Icon className="h-5 w-5" />
                  </Link>
                );
              })}
            </div>

            {/* Additional Info */}
            <div className="text-xs text-muted-foreground">
              {isRTL ? 'נבנה עם ❤️ בישראל' : 'Made with ❤️ for better hiring'}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
