import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Layout } from "@/components/layout";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CVmatic - Smart Resume Analysis Platform",
  description: "Upload, analyze, and improve your resume with AI-powered insights. Get detailed feedback and scoring to land your dream job.",
  keywords: ["resume", "CV", "analysis", "AI", "job", "hiring", "recruitment"],
  authors: [{ name: "CVmatic Team" }],
  creator: "CVmatic",
  publisher: "CVmatic",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://cvmatic.com",
    title: "CVmatic - Smart Resume Analysis Platform",
    description: "Upload, analyze, and improve your resume with AI-powered insights.",
    siteName: "CVmatic",
  },
  twitter: {
    card: "summary_large_image",
    title: "CVmatic - Smart Resume Analysis Platform",
    description: "Upload, analyze, and improve your resume with AI-powered insights.",
    creator: "@cvmatic",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <Layout>
          {children}
        </Layout>
      </body>
    </html>
  );
}
