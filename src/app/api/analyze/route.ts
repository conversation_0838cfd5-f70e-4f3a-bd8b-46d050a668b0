import { NextRequest, NextResponse } from 'next/server';

const CV_ANALYSIS_SERVICE_URL = process.env.CV_ANALYSIS_API_URL || 'http://localhost:8000';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only PDF, DOC, and DOCX files are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size (4MB limit)
    const maxSize = 4 * 1024 * 1024; // 4MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 4MB.' },
        { status: 400 }
      );
    }

    // Prepare form data for microservice
    const microserviceFormData = new FormData();
    microserviceFormData.append('file', file);

    // Add optional parameters
    const targetPosition = formData.get('target_position') as string;
    const targetIndustry = formData.get('target_industry') as string;
    const language = formData.get('language') as string || 'auto';

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (targetPosition) queryParams.append('target_position', targetPosition);
    if (targetIndustry) queryParams.append('target_industry', targetIndustry);
    queryParams.append('language', language);

    // Call CV analysis microservice
    const analysisUrl = `${CV_ANALYSIS_SERVICE_URL}/api/v1/cv-analysis/analyze?${queryParams.toString()}`;

    console.log('Calling CV analysis service:', analysisUrl);

    const response = await fetch(analysisUrl, {
      method: 'POST',
      body: microserviceFormData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('CV analysis service error:', response.status, errorText);

      return NextResponse.json(
        {
          error: 'Analysis service error',
          details: errorText,
          status: response.status
        },
        { status: response.status }
      );
    }

    const analysisResult = await response.json();

    return NextResponse.json({
      success: true,
      data: analysisResult
    });

  } catch (error) {
    console.error('Error in CV analysis API:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'CV Analysis API',
    endpoints: {
      POST: '/api/analyze - Upload and analyze CV file'
    }
  });
}
