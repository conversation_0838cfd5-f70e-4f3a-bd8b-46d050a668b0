import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { message, analysisData, conversationHistory } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Prepare the context for the LLM
    let context = '';
    
    if (analysisData) {
      context += `Context: The user has uploaded a CV that was analyzed. Here's the analysis data:
- Overall Score: ${analysisData.overall_score || 'N/A'}/100
- File: ${analysisData.file_info?.filename || 'Unknown'}
- Language: ${analysisData.file_info?.detected_language || 'Unknown'}
- Analysis Type: ${analysisData.analysis_type || 'Standard'}
- Confidence: ${Math.round((analysisData.confidence || 0) * 100)}%

`;

      if (analysisData.insights) {
        context += `AI Insights:
- Overall Assessment: ${analysisData.insights.overall_assessment || 'N/A'}
- Strengths: ${analysisData.insights.strengths || 'N/A'}
- Areas for Improvement: ${analysisData.insights.improvements || 'N/A'}
- Recommendations: ${analysisData.insights.recommendations || 'N/A'}

`;
      }

      if (analysisData.cv_content) {
        const personalInfo = analysisData.cv_content.personal_info;
        if (personalInfo) {
          context += `Personal Information:
- Name: ${personalInfo.full_name || 'N/A'}
- Email: ${personalInfo.email || 'N/A'}
- Location: ${personalInfo.location || 'N/A'}

`;
        }

        if (analysisData.cv_content.skills && analysisData.cv_content.skills.length > 0) {
          context += `Skills: ${analysisData.cv_content.skills.slice(0, 10).map((skill: any) => 
            typeof skill === 'string' ? skill : skill.name || skill
          ).join(', ')}${analysisData.cv_content.skills.length > 10 ? '...' : ''}

`;
        }

        if (analysisData.cv_content.experience && analysisData.cv_content.experience.length > 0) {
          context += `Work Experience: ${analysisData.cv_content.experience.length} positions listed

`;
        }

        if (analysisData.cv_content.education && analysisData.cv_content.education.length > 0) {
          context += `Education: ${analysisData.cv_content.education.length} entries listed

`;
        }
      }
    } else {
      context = 'Context: The user has not uploaded a CV for analysis yet. Provide general career advice and suggest uploading a CV for personalized insights.';
    }

    // Add conversation history for context
    if (conversationHistory && conversationHistory.length > 0) {
      context += '\nRecent conversation:\n';
      conversationHistory.forEach((msg: any) => {
        context += `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}\n`;
      });
    }

    // Prepare the prompt for the LLM
    const prompt = `You are CVmatic AI, a professional career advisor and CV analysis expert. You speak Hebrew fluently and help users improve their resumes and career prospects.

${context}

User's question: ${message}

Instructions:
1. Respond in Hebrew (עברית) unless the user specifically asks for English
2. Be helpful, professional, and encouraging
3. If you have CV analysis data, reference it specifically in your response
4. Provide actionable advice and concrete suggestions
5. If the user hasn't uploaded a CV, encourage them to do so for personalized advice
6. Keep responses concise but informative (2-3 paragraphs maximum)
7. Use a friendly, supportive tone

Response:`;

    // Call the CV analysis service's LLM endpoint
    const llmResponse = await fetch('http://cv-analysis:8000/api/v1/llm/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: prompt,
        max_tokens: 500,
        temperature: 0.7
      }),
    });

    if (!llmResponse.ok) {
      console.error('LLM service error:', llmResponse.status, llmResponse.statusText);
      
      // Fallback response if LLM service is unavailable
      let fallbackResponse = '';
      
      if (message.includes('שיפור') || message.includes('לשפר')) {
        fallbackResponse = 'כדי לשפר את קורות החיים שלך, אני ממליץ להתמקד בכמה תחומים מרכזיים: הוספת מילות מפתח רלוונטיות לתחום שלך, הדגשת הישגים מדידים במקום רק תיאור תפקידים, ושיפור העיצוב והמבנה של הקורות חיים. האם יש תחום ספציפי שבו תרצה עזרה?';
      } else if (message.includes('חוזקות') || message.includes('חוזקה')) {
        fallbackResponse = analysisData?.insights?.strengths 
          ? `על פי הניתוח שלך, החוזקות העיקריות שלך כוללות: ${analysisData.insights.strengths}. אלה נקודות חזקות שכדאי להדגיש במשרות עתידיות.`
          : 'כדי לזהות את החוזקות שלך, אני צריך לנתח את קורות החיים שלך. העלה את הקובץ שלך ואוכל לספק לך ניתוח מפורט של החוזקות והמיומנויות שלך.';
      } else {
        fallbackResponse = 'אני כאן לעזור לך עם קורות החיים שלך! אתה יכול לשאול אותי על שיפור הקורות חיים, זיהוי חוזקות, התאמה למשרות ספציפיות, או כל נושא אחר הקשור לקריירה. איך אני יכול לעזור לך?';
      }
      
      return NextResponse.json({ response: fallbackResponse });
    }

    const llmData = await llmResponse.json();
    
    return NextResponse.json({
      response: llmData.response || 'מצטער, לא הצלחתי לעבד את השאלה שלך. אנא נסה שוב.',
      model_used: llmData.model_used || 'LLaMA 3.2 1B',
      processing_time: llmData.processing_time || 0
    });

  } catch (error) {
    console.error('Chat API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        response: 'מצטער, אירעה שגיאה בשרת. אנא נסה שוב מאוחר יותר.'
      },
      { status: 500 }
    );
  }
}
