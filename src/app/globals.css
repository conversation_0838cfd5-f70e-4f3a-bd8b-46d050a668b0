@import "tailwindcss";
@import "tw-animate-css";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* CVmatic Brand Colors */
  --color-cvmatic-indigo: #1D223A;
  --color-cvmatic-cyan: #00F0FF;
  --color-cvmatic-gray: #F5F7FA;
  --color-cvmatic-charcoal: #1B1B1F;
  --color-cvmatic-cool-gray: #6D6E76;
  --color-cvmatic-mint: #00E2B3;
  --color-cvmatic-coral: #FF5A5F;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.5rem;
  /* 8px border radius as per brand guidelines */

  /* CVmatic Light Theme */
  --background: #FFFFFF;
  --foreground: #1B1B1F;
  /* Charcoal Black */
  --card: #FFFFFF;
  --card-foreground: #1B1B1F;
  --popover: #FFFFFF;
  --popover-foreground: #1B1B1F;
  --primary: #1D223A;
  /* Deep Indigo */
  --primary-foreground: #FFFFFF;
  --secondary: #F5F7FA;
  /* Soft Gray */
  --secondary-foreground: #1B1B1F;
  --muted: #F5F7FA;
  --muted-foreground: #6D6E76;
  /* Cool Gray */
  --accent: #00F0FF;
  /* Electric Cyan */
  --accent-foreground: #1D223A;
  --destructive: #FF5A5F;
  /* Coral Red */
  --border: #E5E7EB;
  --input: #F5F7FA;
  --ring: #00F0FF;
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* CVmatic Dark Theme */
  --background: #1D223A;
  /* Deep Indigo */
  --foreground: #FFFFFF;
  --card: #2A2F47;
  /* Slightly lighter than background */
  --card-foreground: #FFFFFF;
  --popover: #2A2F47;
  --popover-foreground: #FFFFFF;
  --primary: #00F0FF;
  /* Electric Cyan */
  --primary-foreground: #1D223A;
  --secondary: #2A2F47;
  --secondary-foreground: #FFFFFF;
  --muted: #2A2F47;
  --muted-foreground: #6D6E76;
  /* Cool Gray */
  --accent: #00E2B3;
  /* Fresh Mint */
  --accent-foreground: #1D223A;
  --destructive: #FF5A5F;
  /* Coral Red */
  --border: #3A4058;
  --input: #2A2F47;
  --ring: #00F0FF;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  /* CVmatic Typography */
  h1 {
    @apply text-3xl font-bold text-foreground;
  }

  h2 {
    @apply text-2xl font-semibold text-foreground;
  }

  h3 {
    @apply text-xl font-medium text-foreground;
  }
}

@layer components {

  /* CVmatic Brand Components */
  .cvmatic-gradient {
    background: linear-gradient(135deg, #1D223A 0%, #2A2F47 100%);
  }

  .cvmatic-accent-gradient {
    background: linear-gradient(135deg, #00F0FF 0%, #00E2B3 100%);
  }

  .cvmatic-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-3 font-medium transition-colors;
  }

  .cvmatic-button-accent {
    @apply bg-accent text-accent-foreground hover:bg-accent/90 rounded-lg px-6 py-3 font-medium transition-colors;
  }

  .cvmatic-card {
    @apply bg-card text-card-foreground rounded-lg border border-border shadow-sm;
  }

  .cvmatic-input {
    @apply bg-input border border-border rounded-lg px-3 py-2 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring;
  }
}

@layer utilities {

  /* CVmatic Brand Colors as Utilities */
  .text-cvmatic-indigo {
    color: #1D223A;
  }

  .text-cvmatic-cyan {
    color: #00F0FF;
  }

  .text-cvmatic-gray {
    color: #F5F7FA;
  }

  .text-cvmatic-charcoal {
    color: #1B1B1F;
  }

  .text-cvmatic-cool-gray {
    color: #6D6E76;
  }

  .text-cvmatic-mint {
    color: #00E2B3;
  }

  .text-cvmatic-coral {
    color: #FF5A5F;
  }

  .bg-cvmatic-indigo {
    background-color: #1D223A;
  }

  .bg-cvmatic-cyan {
    background-color: #00F0FF;
  }

  .bg-cvmatic-gray {
    background-color: #F5F7FA;
  }

  .bg-cvmatic-charcoal {
    background-color: #1B1B1F;
  }

  .bg-cvmatic-cool-gray {
    background-color: #6D6E76;
  }

  .bg-cvmatic-mint {
    background-color: #00E2B3;
  }

  .bg-cvmatic-coral {
    background-color: #FF5A5F;
  }

  /* RTL Support */
  .rtl {
    direction: rtl;
  }

  .rtl .text-left {
    text-align: right;
  }

  .rtl .text-right {
    text-align: left;
  }
}