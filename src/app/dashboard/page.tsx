'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  BarChart3,
  FileText,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Download,
  Share,
  RefreshCw,
  Target,
  Brain,
  Zap,
  Users,
  Star,
  ArrowRight,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';

export default function DashboardPage() {
  const [selectedAnalysis, setSelectedAnalysis] = useState(0);
  const [recentAnalysis, setRecentAnalysis] = useState<any>(null);

  useEffect(() => {
    // Load recent analysis from localStorage
    if (typeof window !== 'undefined') {
      const storedAnalysis = localStorage.getItem('cvAnalysisResult');
      if (storedAnalysis) {
        try {
          const parsedAnalysis = JSON.parse(storedAnalysis);
          setRecentAnalysis(parsedAnalysis);
        } catch (error) {
          console.error('Error parsing stored analysis:', error);
        }
      }
    }
  }, []);

  // Dynamic data for CV analysis results - use stored analysis or fallback to mock data
  const analysisResults = recentAnalysis ? [
    {
      id: 1,
      fileName: recentAnalysis.file_info?.filename || 'Unknown_Resume.pdf',
      uploadDate: new Date().toISOString().split('T')[0],
      overallScore: recentAnalysis.overall_score || 0,
      status: 'completed',
      scores: {
        content: Math.min(Math.round((recentAnalysis.confidence || 0.5) * 100), 100),
        ats: Math.min(Math.round((recentAnalysis.confidence || 0.5) * 90), 100),
        formatting: Math.min(Math.round((recentAnalysis.confidence || 0.5) * 85), 100),
        keywords: Math.min(Math.round((recentAnalysis.confidence || 0.5) * 95), 100),
        experience: Math.min(Math.round((recentAnalysis.confidence || 0.5) * 88), 100),
        skills: Math.min(Math.round((recentAnalysis.confidence || 0.5) * 92), 100)
      },
      recommendations: [
        {
          type: 'improvement',
          title: 'Areas for Improvement',
          description: recentAnalysis.insights?.improvements || 'Continue to enhance your resume based on the detailed analysis.',
          impact: 'High'
        },
        {
          type: 'suggestion',
          title: 'AI Recommendations',
          description: recentAnalysis.insights?.recommendations || 'Follow the specific recommendations provided in the analysis.',
          impact: 'Medium'
        }
      ],
      strengths: recentAnalysis.insights?.strengths ?
        recentAnalysis.insights.strengths.split('.').filter(s => s.trim()).slice(0, 4) :
        ['AI analysis completed', 'Resume processed successfully'],
      improvements: recentAnalysis.insights?.improvements ?
        recentAnalysis.insights.improvements.split('.').filter(s => s.trim()).slice(0, 4) :
        ['Upload a new resume for detailed analysis']
    }
  ] : [
    {
      id: 1,
      fileName: 'No Analysis Available',
      uploadDate: new Date().toISOString().split('T')[0],
      overallScore: 0,
      status: 'pending',
      scores: {
        content: 0,
        ats: 0,
        formatting: 0,
        keywords: 0,
        experience: 0,
        skills: 0
      },
      recommendations: [
        {
          type: 'info',
          title: 'Upload a Resume',
          description: 'Please upload a resume to see detailed analysis results.',
          impact: 'High'
        }
      ],
      strengths: ['Upload a resume to see analysis'],
      improvements: ['No analysis data available']
    }
  ];

  const currentAnalysis = analysisResults[selectedAnalysis];

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return { label: 'Excellent', variant: 'default' as const, className: 'bg-green-100 text-green-700' };
    if (score >= 80) return { label: 'Good', variant: 'secondary' as const, className: 'bg-blue-100 text-blue-700' };
    if (score >= 70) return { label: 'Fair', variant: 'secondary' as const, className: 'bg-yellow-100 text-yellow-700' };
    return { label: 'Needs Work', variant: 'destructive' as const, className: 'bg-red-100 text-red-700' };
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold">CV Analysis Dashboard</h1>
            <p className="text-muted-foreground">
              Review your resume analysis results and recommendations
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button className="cvmatic-button-primary">
              <FileText className="h-4 w-4 mr-2" />
              Upload New CV
            </Button>
          </div>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar - Analysis List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Your Analyses</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-2">
                  {analysisResults.map((analysis, index) => (
                    <div
                      key={analysis.id}
                      className={`p-4 cursor-pointer transition-colors border-l-4 ${selectedAnalysis === index
                        ? 'bg-primary/5 border-l-primary'
                        : 'hover:bg-muted/50 border-l-transparent'
                        }`}
                      onClick={() => setSelectedAnalysis(index)}
                    >
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium truncate">
                            {analysis.fileName}
                          </span>
                          <Badge className={getScoreBadge(analysis.overallScore).className}>
                            {analysis.overallScore}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(analysis.uploadDate).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Recent Analysis Alert */}
            {recentAnalysis && (
              <Alert className="border-green-200 bg-green-50">
                <Brain className="h-4 w-4" />
                <AlertDescription>
                  <div className="flex items-center justify-between">
                    <div>
                      <strong>Latest Analysis Complete!</strong> Your resume scored{' '}
                      <span className="font-bold text-green-700">
                        {recentAnalysis.overall_score}/100
                      </span>
                      {recentAnalysis.score_category && (
                        <span className="ml-2 text-sm">({recentAnalysis.score_category})</span>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setRecentAnalysis(null)}
                    >
                      Dismiss
                    </Button>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Overall Score */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      {currentAnalysis.fileName}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      Analyzed on {new Date(currentAnalysis.uploadDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button variant="outline" size="sm">
                      <Share className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-8">
                  <div className="text-center">
                    <div className={`text-6xl font-bold mb-2 ${getScoreColor(currentAnalysis.overallScore)}`}>
                      {currentAnalysis.overallScore}
                      <span className="text-2xl text-muted-foreground">/100</span>
                    </div>
                    <Badge className={getScoreBadge(currentAnalysis.overallScore).className}>
                      {getScoreBadge(currentAnalysis.overallScore).label}
                    </Badge>
                    <p className="text-sm text-muted-foreground mt-2">Overall Score</p>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-semibold">Score Breakdown</h4>
                    {Object.entries(currentAnalysis.scores).map(([category, score]) => (
                      <div key={category} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="capitalize">{category.replace(/([A-Z])/g, ' $1')}</span>
                          <span className={`font-medium ${getScoreColor(score)}`}>{score}%</span>
                        </div>
                        <Progress value={score} className="h-2" />
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {currentAnalysis.recommendations.map((rec, index) => (
                  <Alert key={index} className={
                    rec.type === 'critical' ? 'border-red-200 bg-red-50' :
                      rec.type === 'improvement' ? 'border-yellow-200 bg-yellow-50' :
                        'border-blue-200 bg-blue-50'
                  }>
                    <div className="flex items-start gap-3">
                      {rec.type === 'critical' ? (
                        <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                      ) : rec.type === 'improvement' ? (
                        <TrendingUp className="h-5 w-5 text-yellow-600 mt-0.5" />
                      ) : (
                        <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                      )}
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h5 className="font-medium">{rec.title}</h5>
                          <Badge variant="outline" className="text-xs">
                            {rec.impact} Impact
                          </Badge>
                        </div>
                        <AlertDescription className="text-sm">
                          {rec.description}
                        </AlertDescription>
                      </div>
                    </div>
                  </Alert>
                ))}
              </CardContent>
            </Card>

            {/* Strengths & Improvements */}
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-600">
                    <CheckCircle className="h-5 w-5" />
                    Strengths
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {currentAnalysis.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <Star className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>{strength}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-600">
                    <TrendingUp className="h-5 w-5" />
                    Areas for Improvement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {currentAnalysis.improvements.map((improvement, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <ArrowRight className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
                        <span>{improvement}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* LLM Analysis Results */}
            {recentAnalysis && recentAnalysis.insights && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-purple-600" />
                    AI-Powered Insights
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Advanced analysis powered by {recentAnalysis.model_used || 'AI'}
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4 text-sm">
                      <Badge variant="outline">
                        Analysis Type: {recentAnalysis.analysis_type}
                      </Badge>
                      <Badge variant="outline">
                        Confidence: {Math.round((recentAnalysis.confidence || 0) * 100)}%
                      </Badge>
                      {recentAnalysis.processing_time && (
                        <Badge variant="outline">
                          Processing Time: {recentAnalysis.processing_time.toFixed(1)}s
                        </Badge>
                      )}
                    </div>

                    <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
                      <h4 className="font-medium mb-3">AI Analysis Results:</h4>
                      <div className="space-y-3 text-sm text-gray-700">
                        {recentAnalysis.insights && typeof recentAnalysis.insights === 'object' ? (
                          <>
                            {recentAnalysis.insights.overall_assessment && (
                              <div>
                                <h5 className="font-medium text-gray-900 mb-1">Overall Assessment:</h5>
                                <p className="text-gray-700">{recentAnalysis.insights.overall_assessment}</p>
                              </div>
                            )}
                            {recentAnalysis.insights.strengths && (
                              <div>
                                <h5 className="font-medium text-green-700 mb-1">Key Strengths:</h5>
                                <p className="text-gray-700">{recentAnalysis.insights.strengths}</p>
                              </div>
                            )}
                            {recentAnalysis.insights.improvements && (
                              <div>
                                <h5 className="font-medium text-orange-700 mb-1">Areas for Improvement:</h5>
                                <p className="text-gray-700">{recentAnalysis.insights.improvements}</p>
                              </div>
                            )}
                            {recentAnalysis.insights.recommendations && (
                              <div>
                                <h5 className="font-medium text-blue-700 mb-1">Recommendations:</h5>
                                <p className="text-gray-700">{recentAnalysis.insights.recommendations}</p>
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-gray-600">
                            {typeof recentAnalysis.insights === 'string'
                              ? recentAnalysis.insights
                              : 'AI analysis completed. Upload a new resume for detailed insights.'}
                          </div>
                        )}
                      </div>
                    </div>

                    {recentAnalysis.file_info && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Language:</span>
                          <div className="font-medium">
                            {recentAnalysis.file_info.detected_language?.toUpperCase() || 'N/A'}
                          </div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Text Quality:</span>
                          <div className="font-medium">
                            {Math.round((recentAnalysis.file_info.text_quality || 0) * 100)}%
                          </div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Text Length:</span>
                          <div className="font-medium">
                            {recentAnalysis.file_info.text_length || 0} chars
                          </div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">File:</span>
                          <div className="font-medium truncate">
                            {recentAnalysis.file_info.filename || 'N/A'}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="cvmatic-button-primary">
                    <Edit className="h-5 w-5 mr-2" />
                    Improve Resume
                  </Button>
                  <Button variant="outline" size="lg">
                    <Download className="h-5 w-5 mr-2" />
                    Download Report
                  </Button>
                  <Button variant="outline" size="lg">
                    <FileText className="h-5 w-5 mr-2" />
                    Upload New Version
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
