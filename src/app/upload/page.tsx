'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  Brain,
  Target,
  Zap,
  Shield,
  ArrowRight,
  BarChart3
} from 'lucide-react';
import FileUpload from '@/components/upload/FileUpload';

export default function UploadPage() {
  const router = useRouter();
  const [analysisStep, setAnalysisStep] = useState<'upload' | 'analyzing' | 'complete'>('upload');
  const [analysisResult, setAnalysisResult] = useState<any>(null);

  const handleAnalysisComplete = (result: any) => {
    console.log('Analysis completed:', result);
    setAnalysisResult(result);
    setAnalysisStep('complete');
  };

  const handleViewDetailedResults = () => {
    // Store analysis result in localStorage for dashboard
    if (analysisResult) {
      localStorage.setItem('cvAnalysisResult', JSON.stringify(analysisResult));
    }
    router.push('/dashboard');
  };

  const features = [
    {
      icon: Brain,
      title: 'AI Content Analysis',
      description: 'Deep analysis of your resume content, skills, and experience relevance.'
    },
    {
      icon: Target,
      title: 'ATS Optimization',
      description: 'Ensure your resume passes Applicant Tracking Systems with flying colors.'
    },
    {
      icon: Zap,
      title: 'Instant Feedback',
      description: 'Get immediate insights and recommendations for improvement.'
    },
    {
      icon: Shield,
      title: 'Privacy Protected',
      description: 'Your data is secure and never stored permanently on our servers.'
    }
  ];

  const analysisSteps = [
    'Parsing document structure...',
    'Analyzing content quality...',
    'Checking ATS compatibility...',
    'Evaluating keyword density...',
    'Assessing formatting...',
    'Generating recommendations...',
    'Calculating final score...'
  ];

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center space-y-4 mb-12">
          <Badge variant="secondary" className="w-fit mx-auto">
            <Upload className="h-3 w-3 mr-1" />
            Resume Analysis
          </Badge>
          <h1 className="text-3xl lg:text-5xl font-bold">
            Upload Your Resume
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Get comprehensive AI-powered analysis of your resume with detailed feedback,
            scoring, and actionable recommendations for improvement.
          </p>
        </div>

        {analysisStep === 'upload' && (
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Upload Section */}
            <div className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Upload Resume
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <FileUpload
                    onAnalysisComplete={handleAnalysisComplete}
                    useLLM={true}
                    analysisType="skills_extraction"
                  />
                </CardContent>
              </Card>

              {/* Requirements */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Requirements</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>PDF, DOC, or DOCX format</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Maximum file size: 4MB</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Text-based content (not scanned images)</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>English or Hebrew language</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Features Section */}
            <div className="space-y-6">
              <h3 className="text-2xl font-semibold">What You'll Get</h3>

              <div className="space-y-4">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <Card key={index} className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="bg-primary/10 text-primary rounded-lg p-2 flex-shrink-0">
                          <Icon className="h-5 w-5" />
                        </div>
                        <div>
                          <h4 className="font-semibold mb-1">{feature.title}</h4>
                          <p className="text-sm text-muted-foreground">{feature.description}</p>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>

              {/* Sample Score */}
              <Card className="p-6 bg-gradient-to-br from-primary/5 to-accent/5">
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold text-primary">85/100</div>
                  <div className="text-sm text-muted-foreground">Sample Score</div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Content Quality</span>
                      <span className="font-medium">92%</span>
                    </div>
                    <Progress value={92} className="h-2" />
                    <div className="flex justify-between text-sm">
                      <span>ATS Compatibility</span>
                      <span className="font-medium">78%</span>
                    </div>
                    <Progress value={78} className="h-2" />
                    <div className="flex justify-between text-sm">
                      <span>Formatting</span>
                      <span className="font-medium">85%</span>
                    </div>
                    <Progress value={85} className="h-2" />
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {analysisStep === 'analyzing' && (
          <div className="max-w-2xl mx-auto">
            <Card className="p-8">
              <div className="text-center space-y-6">
                <div className="bg-primary/10 text-primary rounded-full p-4 w-fit mx-auto">
                  <Brain className="h-8 w-8 animate-pulse" />
                </div>

                <div className="space-y-2">
                  <h2 className="text-2xl font-bold">Analyzing Your Resume</h2>
                  <p className="text-muted-foreground">
                    Our AI is carefully reviewing your resume. This usually takes 30-60 seconds.
                  </p>
                </div>



                <div className="text-left space-y-2 max-w-sm mx-auto">
                  {analysisSteps.map((step, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="h-4 w-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                      <span>{step}</span>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        )}

        {analysisStep === 'complete' && (
          <div className="max-w-2xl mx-auto">
            <Card className="p-8">
              <div className="text-center space-y-6">
                <div className="bg-green-100 text-green-600 rounded-full p-4 w-fit mx-auto">
                  <CheckCircle className="h-8 w-8" />
                </div>

                <div className="space-y-2">
                  <h2 className="text-2xl font-bold">Analysis Complete!</h2>
                  <p className="text-muted-foreground">
                    Your resume has been successfully analyzed. View your detailed results below.
                  </p>
                </div>

                <div className="bg-gradient-to-br from-primary/5 to-accent/5 rounded-lg p-6">
                  {analysisResult ? (
                    <>
                      <div className="text-4xl font-bold text-primary mb-2">
                        {analysisResult.overall_score || 'N/A'}/100
                      </div>
                      <div className="text-sm text-muted-foreground mb-4">Overall Score</div>
                      <Badge variant="secondary" className={
                        analysisResult.overall_score >= 80 ? 'bg-green-100 text-green-700' :
                          analysisResult.overall_score >= 60 ? 'bg-yellow-100 text-yellow-700' :
                            'bg-red-100 text-red-700'
                      }>
                        {analysisResult.score_category ||
                          (analysisResult.overall_score >= 80 ? 'Strong Resume' :
                            analysisResult.overall_score >= 60 ? 'Good Resume' : 'Needs Improvement')}
                      </Badge>

                      {/* Show LLM insights preview */}
                      {analysisResult.insights && (
                        <div className="mt-4 p-3 bg-white/50 rounded-lg">
                          <div className="text-sm font-medium mb-2">AI Insights:</div>
                          <div className="text-xs text-muted-foreground line-clamp-3">
                            {typeof analysisResult.insights === 'string'
                              ? analysisResult.insights.substring(0, 150) + '...'
                              : JSON.stringify(analysisResult.insights).substring(0, 150) + '...'}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <>
                      <div className="text-4xl font-bold text-primary mb-2">87/100</div>
                      <div className="text-sm text-muted-foreground mb-4">Overall Score</div>
                      <Badge variant="secondary" className="bg-green-100 text-green-700">
                        Strong Resume
                      </Badge>
                    </>
                  )}
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Button size="lg" className="cvmatic-button-primary" onClick={handleViewDetailedResults}>
                    <BarChart3 className="h-5 w-5 mr-2" />
                    View Detailed Results
                  </Button>
                  <Button variant="outline" size="lg">
                    Download Report
                    <ArrowRight className="h-5 w-5 ml-2" />
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
