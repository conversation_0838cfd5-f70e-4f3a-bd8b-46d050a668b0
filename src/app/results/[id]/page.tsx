// הוספת קומפוננטה להצגת תובנות ה-AI
import { Brain, Lightbulb, TrendingUp, Award, AlertTriangle } from 'lucide-react';

// קומפוננטה להצגת תובנות ה-AI
const AIInsightsSection = ({ analysisData }) => {
  const llmInsights = analysisData?.analysis_metadata?.llm_insights;
  
  if (!llmInsights) {
    return null;
  }
  
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-600" />
          AI-Powered Insights
        </CardTitle>
        <CardDescription>
          Advanced analysis powered by machine learning
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* תקציר התובנות */}
        {llmInsights.summary && (
          <div className="mb-6 p-4 bg-purple-50 rounded-lg">
            <h3 className="text-md font-medium mb-3 text-purple-800">Key Insights:</h3>
            
            {/* התרשמות כללית */}
            {llmInsights.summary.overall_impression && (
              <div className="mb-3">
                <p className="text-sm text-gray-700">{llmInsights.summary.overall_impression}</p>
              </div>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              {/* חוזקות */}
              {llmInsights.summary.key_strengths && llmInsights.summary.key_strengths.length > 0 && (
                <div className="flex items-start gap-2">
                  <Award className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Strengths</h4>
                    <ul className="mt-1 text-sm text-gray-700">
                      {llmInsights.summary.key_strengths.map((strength, i) => (
                        <li key={i} className="flex items-center gap-1">
                          <span>•</span> {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
              
              {/* תחומים לשיפור */}
              {llmInsights.summary.improvement_areas && llmInsights.summary.improvement_areas.length > 0 && (
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Areas for Improvement</h4>
                    <ul className="mt-1 text-sm text-gray-700">
                      {llmInsights.summary.improvement_areas.map((area, i) => (
                        <li key={i} className="flex items-center gap-1">
                          <span>•</span> {area}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
              
              {/* מיומנויות בולטות */}
              {llmInsights.summary.notable_skills && llmInsights.summary.notable_skills.length > 0 && (
                <div className="flex items-start gap-2">
                  <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Notable Skills</h4>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {llmInsights.summary.notable_skills.map((skill, i) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )}
              
              {/* תובנות קריירה */}
              {llmInsights.summary.career_insights && (
                <div className="flex items-start gap-2">
                  <TrendingUp className="h-5 w-5 text-indigo-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Career Insights</h4>
                    <p className="mt-1 text-sm text-gray-700">{llmInsights.summary.career_insights}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* תובנות מפורטות */}
        <div className="space-y-6">
          {/* ניתוח מיומנויות */}
          {llmInsights.enhanced_skills && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-blue-100 rounded text-blue-700">
                  <Lightbulb className="h-4 w-4" />
                </span>
                Skills Analysis
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* מיומנויות טכניות */}
                {llmInsights.enhanced_skills.technical_skills && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Technical Skills</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.enhanced_skills.technical_skills.map((skill, i) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* מיומנויות רכות */}
                {llmInsights.enhanced_skills.soft_skills && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Soft Skills</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.enhanced_skills.soft_skills.map((skill, i) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* שפות */}
                {llmInsights.enhanced_skills.languages && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Languages</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.enhanced_skills.languages.map((lang, i) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          {lang}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* כלים וטכנולוגיות */}
                {llmInsights.enhanced_skills.tools_and_technologies && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Tools & Technologies</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.enhanced_skills.tools_and_technologies.map((tool, i) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                          {tool}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* ניתוח ניסיון */}
          {llmInsights.experience_insights && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-green-100 rounded text-green-700">
                  <TrendingUp className="h-4 w-4" />
                </span>
                Experience Analysis
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* רמת ניסיון */}
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Experience Level</h4>
                  <p className="text-sm">{llmInsights.experience_insights.experience_level || 'Not specified'}</p>
                  <p className="text-sm mt-1">
                    <span className="font-medium">Total Experience:</span> {llmInsights.experience_insights.total_years_experience || 'Unknown'} years
                  </p>
                </div>
                
                {/* התקדמות קריירה */}
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Career Progression</h4>
                  <p className="text-sm">{llmInsights.experience_insights.career_progression || 'Not available'}</p>
                </div>
                
                {/* ניסיון בתעשייה */}
                {llmInsights.experience_insights.industry_experience && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Industry Experience</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.experience_insights.industry_experience.map((industry, i) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                          {industry}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* הישגים מרכזיים */}
                {llmInsights.experience_insights.key_achievements && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Key Achievements</h4>
                    <ul className="text-sm list-disc pl-5">
                      {llmInsights.experience_insights.key_achievements.map((achievement, i) => (
                        <li key={i}>{achievement}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* הערכת איכות */}
          {llmInsights.quality_assessment && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-amber-100 rounded text-amber-700">
                  <Award className="h-4 w-4" />
                </span>
                Quality Assessment
              </h3>
              
              {/* ציוני איכות */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
                {llmInsights.quality_assessment.completeness_score !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-2xl font-bold">{llmInsights.quality_assessment.completeness_score}</div>
                    <div className="text-xs text-gray-500">Completeness</div>
                  </div>
                )}
                
                {llmInsights.quality_assessment.clarity_score !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-2xl font-bold">{llmInsights.quality_assessment.clarity_score}</div>
                    <div className="text-xs text-gray-500">Clarity</div>
                  </div>
                )}
                
                {llmInsights.quality_assessment.professional_tone !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-2xl font-bold">{llmInsights.quality_assessment.professional_tone}</div>
                    <div className="text-xs text-gray-500">Professional Tone</div>
                  </div>
                )}
                
                {llmInsights.quality_assessment.structure_quality !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-2xl font-bold">{llmInsights.quality_assessment.structure_quality}</div>
                    <div className="text-xs text-gray-500">Structure</div>
                  </div>
                )}
              </div>
              
              {/* המלצות לשיפור */}
              {llmInsights.quality_assessment.improvement_suggestions && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Improvement Suggestions</h4>
                  <ul className="text-sm list-disc pl-5">
                    {llmInsights.quality_assessment.improvement_suggestions.map((suggestion, i) => (
                      <li key={i}>{suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {/* חלקים חסרים */}
              {llmInsights.quality_assessment.missing_sections && llmInsights.quality_assessment.missing_sections.length > 0 && (
                <div className="p-3 bg-gray-50 rounded-lg mt-3">
                  <h4 className="text-sm font-medium mb-2">Missing Sections</h4>
                  <div className="flex flex-wrap gap-1">
                    {llmInsights.quality_assessment.missing_sections.map((section, i) => (
                      <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        {section}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* הערכה כללית */}
          {llmInsights.overall_assessment && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-purple-100 rounded text-purple-700">
                  <Brain className="h-4 w-4" />
                </span>
                Overall Assessment
              </h3>
              
              <div className="space-y-3">
                {/* הערכה כללית */}
                {llmInsights.overall_assessment.overall_assessment && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Overall Assessment</h4>
                    <p className="text-sm">{llmInsights.overall_assessment.overall_assessment}</p>
                  </div>
                )}
                
                {/* חוזקות */}
                {llmInsights.overall_assessment.strengths && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Strengths</h4>
                    <p className="text-sm">{llmInsights.overall_assessment.strengths}</p>
                  </div>
                )}
                
                {/* תחומים לשיפור */}
                {llmInsights.overall_assessment.improvements && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Areas for Improvement</h4>
                    <p className="text-sm">{llmInsights.overall_assessment.improvements}</p>
                  </div>
                )}
                
                {/* המלצות */}
                {llmInsights.overall_assessment.recommendations && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Recommendations</h4>
                    <p className="text-sm">{llmInsights.overall_assessment.recommendations}</p>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* התאמה למשרה */}
          {llmInsights.job_matching && target_position && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-indigo-100 rounded text-indigo-700">
                  <CheckCircle className="h-4 w-4" />
                </span>
                Job Matching: {target_position}
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* ציון התאמה */}
                {llmInsights.job_matching.match_score !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Match Score</h4>
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-blue-600 h-2.5 rounded-full" 
                          style={{ width: `${llmInsights.job_matching.match_score}%` }}
                        ></div>
                      </div>
                      <span className="ml-2 text-sm font-medium">{llmInsights.job_matching.match_score}%</span>
                    </div>
                    <p className="text-sm mt-2">
                      Overall fit: <span className="font-medium">{llmInsights.job_matching.overall_fit || 'Not specified'}</span>
                    </p>
                  </div>
                )}
                
                {/* מיומנויות תואמות */}
                {llmInsights.job_matching.matching_skills && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Matching Skills</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.job_matching.matching_skills.map((skill, i) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* מיומנויות חסרות */}
                {llmInsights.job_matching.missing_skills && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Missing Skills</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.job_matching.missing_skills.map((skill, i) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* המלצות */}
                {llmInsights.job_matching.recommendations && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Recommendations</h4>
                    <ul className="text-sm list-disc pl-5">
                      {llmInsights.job_matching.recommendations.map((recommendation, i) => (
                        <li key={i}>{recommendation}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// הוספת הקומפוננטה לדף התוצאות
// בתוך הפונקציה הראשית של הדף, הוסף:
<AIInsightsSection analysisData={analysisData} />