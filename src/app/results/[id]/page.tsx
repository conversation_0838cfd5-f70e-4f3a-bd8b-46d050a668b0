'use client';

import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Brain,
  Lightbulb,
  TrendingUp,
  Award,
  AlertTriangle,
  CheckCircle,
  ArrowLeft,
  Download,
  Share2,
  FileText,
  BarChart3,
  User,
  Briefcase,
  GraduationCap,
  Languages
} from 'lucide-react';

// קומפוננטה להצגת תובנות ה-AI
const AIInsightsSection = ({ analysisData }: { analysisData: any }) => {
  const llmInsights = analysisData?.analysis_metadata?.llm_insights;

  if (!llmInsights) {
    return null;
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-600" />
          AI-Powered Insights
        </CardTitle>
        <CardDescription>
          Advanced analysis powered by machine learning
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* תקציר התובנות */}
        {llmInsights.summary && (
          <div className="mb-6 p-4 bg-purple-50 rounded-lg">
            <h3 className="text-md font-medium mb-3 text-purple-800">Key Insights:</h3>

            {/* התרשמות כללית */}
            {llmInsights.summary.overall_impression && (
              <div className="mb-3">
                <p className="text-sm text-gray-700">{llmInsights.summary.overall_impression}</p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              {/* חוזקות */}
              {llmInsights.summary.key_strengths && llmInsights.summary.key_strengths.length > 0 && (
                <div className="flex items-start gap-2">
                  <Award className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Strengths</h4>
                    <ul className="mt-1 text-sm text-gray-700">
                      {llmInsights.summary.key_strengths.map((strength: string, i: number) => (
                        <li key={i} className="flex items-center gap-1">
                          <span>•</span> {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* תחומים לשיפור */}
              {llmInsights.summary.improvement_areas && llmInsights.summary.improvement_areas.length > 0 && (
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Areas for Improvement</h4>
                    <ul className="mt-1 text-sm text-gray-700">
                      {llmInsights.summary.improvement_areas.map((area: string, i: number) => (
                        <li key={i} className="flex items-center gap-1">
                          <span>•</span> {area}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* מיומנויות בולטות */}
              {llmInsights.summary.notable_skills && llmInsights.summary.notable_skills.length > 0 && (
                <div className="flex items-start gap-2">
                  <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Notable Skills</h4>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {llmInsights.summary.notable_skills.map((skill: string, i: number) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* תובנות קריירה */}
              {llmInsights.summary.career_insights && (
                <div className="flex items-start gap-2">
                  <TrendingUp className="h-5 w-5 text-indigo-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Career Insights</h4>
                    <p className="mt-1 text-sm text-gray-700">{llmInsights.summary.career_insights}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* תובנות מפורטות */}
        <div className="space-y-6">
          {/* ניתוח מיומנויות */}
          {llmInsights.enhanced_skills && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-blue-100 rounded text-blue-700">
                  <Lightbulb className="h-4 w-4" />
                </span>
                Skills Analysis
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* מיומנויות טכניות */}
                {llmInsights.enhanced_skills.technical_skills && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Technical Skills</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.enhanced_skills.technical_skills.map((skill: string, i: number) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* מיומנויות רכות */}
                {llmInsights.enhanced_skills.soft_skills && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Soft Skills</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.enhanced_skills.soft_skills.map((skill: string, i: number) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* שפות */}
                {llmInsights.enhanced_skills.languages && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Languages</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.enhanced_skills.languages.map((lang: string, i: number) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          {lang}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* כלים וטכנולוגיות */}
                {llmInsights.enhanced_skills.tools_and_technologies && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Tools & Technologies</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.enhanced_skills.tools_and_technologies.map((tool: string, i: number) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                          {tool}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* ניתוח ניסיון */}
          {llmInsights.experience_insights && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-green-100 rounded text-green-700">
                  <TrendingUp className="h-4 w-4" />
                </span>
                Experience Analysis
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* רמת ניסיון */}
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Experience Level</h4>
                  <p className="text-sm">{llmInsights.experience_insights.experience_level || 'Not specified'}</p>
                  <p className="text-sm mt-1">
                    <span className="font-medium">Total Experience:</span> {llmInsights.experience_insights.total_years_experience || 'Unknown'} years
                  </p>
                </div>

                {/* התקדמות קריירה */}
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Career Progression</h4>
                  <p className="text-sm">{llmInsights.experience_insights.career_progression || 'Not available'}</p>
                </div>

                {/* ניסיון בתעשייה */}
                {llmInsights.experience_insights.industry_experience && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Industry Experience</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.experience_insights.industry_experience.map((industry: string, i: number) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                          {industry}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* הישגים מרכזיים */}
                {llmInsights.experience_insights.key_achievements && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Key Achievements</h4>
                    <ul className="text-sm list-disc pl-5">
                      {llmInsights.experience_insights.key_achievements.map((achievement: string, i: number) => (
                        <li key={i}>{achievement}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* הערכת איכות */}
          {llmInsights.quality_assessment && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-amber-100 rounded text-amber-700">
                  <Award className="h-4 w-4" />
                </span>
                Quality Assessment
              </h3>

              {/* ציוני איכות */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
                {llmInsights.quality_assessment.completeness_score !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-2xl font-bold">{llmInsights.quality_assessment.completeness_score}</div>
                    <div className="text-xs text-gray-500">Completeness</div>
                  </div>
                )}

                {llmInsights.quality_assessment.clarity_score !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-2xl font-bold">{llmInsights.quality_assessment.clarity_score}</div>
                    <div className="text-xs text-gray-500">Clarity</div>
                  </div>
                )}

                {llmInsights.quality_assessment.professional_tone !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-2xl font-bold">{llmInsights.quality_assessment.professional_tone}</div>
                    <div className="text-xs text-gray-500">Professional Tone</div>
                  </div>
                )}

                {llmInsights.quality_assessment.structure_quality !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-2xl font-bold">{llmInsights.quality_assessment.structure_quality}</div>
                    <div className="text-xs text-gray-500">Structure</div>
                  </div>
                )}
              </div>

              {/* המלצות לשיפור */}
              {llmInsights.quality_assessment.improvement_suggestions && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Improvement Suggestions</h4>
                  <ul className="text-sm list-disc pl-5">
                    {llmInsights.quality_assessment.improvement_suggestions.map((suggestion: string, i: number) => (
                      <li key={i}>{suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* חלקים חסרים */}
              {llmInsights.quality_assessment.missing_sections && llmInsights.quality_assessment.missing_sections.length > 0 && (
                <div className="p-3 bg-gray-50 rounded-lg mt-3">
                  <h4 className="text-sm font-medium mb-2">Missing Sections</h4>
                  <div className="flex flex-wrap gap-1">
                    {llmInsights.quality_assessment.missing_sections.map((section, i) => (
                      <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        {section}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* הערכה כללית */}
          {llmInsights.overall_assessment && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-purple-100 rounded text-purple-700">
                  <Brain className="h-4 w-4" />
                </span>
                Overall Assessment
              </h3>

              <div className="space-y-3">
                {/* הערכה כללית */}
                {llmInsights.overall_assessment.overall_assessment && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Overall Assessment</h4>
                    <p className="text-sm">{llmInsights.overall_assessment.overall_assessment}</p>
                  </div>
                )}

                {/* חוזקות */}
                {llmInsights.overall_assessment.strengths && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Strengths</h4>
                    <p className="text-sm">{llmInsights.overall_assessment.strengths}</p>
                  </div>
                )}

                {/* תחומים לשיפור */}
                {llmInsights.overall_assessment.improvements && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Areas for Improvement</h4>
                    <p className="text-sm">{llmInsights.overall_assessment.improvements}</p>
                  </div>
                )}

                {/* המלצות */}
                {llmInsights.overall_assessment.recommendations && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Recommendations</h4>
                    <p className="text-sm">{llmInsights.overall_assessment.recommendations}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* התאמה למשרה */}
          {llmInsights.job_matching && (
            <div>
              <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                <span className="p-1 bg-indigo-100 rounded text-indigo-700">
                  <CheckCircle className="h-4 w-4" />
                </span>
                Job Matching: {target_position}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* ציון התאמה */}
                {llmInsights.job_matching.match_score !== undefined && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Match Score</h4>
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-blue-600 h-2.5 rounded-full"
                          style={{ width: `${llmInsights.job_matching.match_score}%` }}
                        ></div>
                      </div>
                      <span className="ml-2 text-sm font-medium">{llmInsights.job_matching.match_score}%</span>
                    </div>
                    <p className="text-sm mt-2">
                      Overall fit: <span className="font-medium">{llmInsights.job_matching.overall_fit || 'Not specified'}</span>
                    </p>
                  </div>
                )}

                {/* מיומנויות תואמות */}
                {llmInsights.job_matching.matching_skills && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Matching Skills</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.job_matching.matching_skills.map((skill: string, i: number) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* מיומנויות חסרות */}
                {llmInsights.job_matching.missing_skills && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Missing Skills</h4>
                    <div className="flex flex-wrap gap-1">
                      {llmInsights.job_matching.missing_skills.map((skill: string, i: number) => (
                        <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* המלצות */}
                {llmInsights.job_matching.recommendations && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Recommendations</h4>
                    <ul className="text-sm list-disc pl-5">
                      {llmInsights.job_matching.recommendations.map((recommendation: string, i: number) => (
                        <li key={i}>{recommendation}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Main Results Page Component
export default function ResultsPage() {
  const params = useParams();
  const router = useRouter();
  const [analysisData, setAnalysisData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load analysis data from localStorage or API
    const loadAnalysisData = () => {
      try {
        const storedData = localStorage.getItem('cvAnalysisResult');
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          setAnalysisData(parsedData);
        } else {
          // If no stored data, redirect to upload page
          router.push('/upload');
        }
      } catch (error) {
        console.error('Error loading analysis data:', error);
        router.push('/upload');
      } finally {
        setLoading(false);
      }
    };

    loadAnalysisData();
  }, [params.id, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading analysis results...</p>
        </div>
      </div>
    );
  }

  if (!analysisData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">No Analysis Found</h2>
          <p className="text-muted-foreground mb-4">We couldn't find the analysis results you're looking for.</p>
          <Button onClick={() => router.push('/upload')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Upload
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-3xl font-bold">CV Analysis Results</h1>
              <p className="text-muted-foreground">
                {analysisData?.analysis_metadata?.filename || 'Resume Analysis'}
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download Report
            </Button>
          </div>
        </div>

        {/* Overall Score Card */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              Overall Assessment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-4xl font-bold text-primary mb-2">
                  {analysisData?.overall_score || analysisData?.score_breakdown?.overall_score || 'N/A'}/100
                </div>
                <div className="text-sm text-muted-foreground">Overall Score</div>
                <Badge
                  variant="secondary"
                  className={`mt-2 ${(analysisData?.overall_score || 0) >= 80
                    ? 'bg-green-100 text-green-700'
                    : (analysisData?.overall_score || 0) >= 60
                      ? 'bg-yellow-100 text-yellow-700'
                      : 'bg-red-100 text-red-700'
                    }`}
                >
                  {(analysisData?.overall_score || 0) >= 80
                    ? 'Excellent'
                    : (analysisData?.overall_score || 0) >= 60
                      ? 'Good'
                      : 'Needs Improvement'}
                </Badge>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold mb-2">
                  {analysisData?.score_breakdown?.completeness_score || 'N/A'}
                </div>
                <div className="text-sm text-muted-foreground">Completeness</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold mb-2">
                  {analysisData?.score_breakdown?.relevance_score || 'N/A'}
                </div>
                <div className="text-sm text-muted-foreground">Relevance</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold mb-2">
                  {analysisData?.score_breakdown?.presentation_score || 'N/A'}
                </div>
                <div className="text-sm text-muted-foreground">Presentation</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* AI Insights Section */}
        <AIInsightsSection analysisData={analysisData} />

        {/* CV Content Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Personal Information */}
          {analysisData?.cv_content?.personal_info && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5 text-blue-600" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analysisData.cv_content.personal_info.full_name && (
                    <div>
                      <span className="font-medium">Name:</span> {analysisData.cv_content.personal_info.full_name}
                    </div>
                  )}
                  {analysisData.cv_content.personal_info.email && (
                    <div>
                      <span className="font-medium">Email:</span> {analysisData.cv_content.personal_info.email}
                    </div>
                  )}
                  {analysisData.cv_content.personal_info.phone && (
                    <div>
                      <span className="font-medium">Phone:</span> {analysisData.cv_content.personal_info.phone}
                    </div>
                  )}
                  {analysisData.cv_content.personal_info.location && (
                    <div>
                      <span className="font-medium">Location:</span> {analysisData.cv_content.personal_info.location}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Experience Level */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5 text-green-600" />
                Experience Level
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-2xl font-bold mb-2">
                  {analysisData?.experience_level || 'Not Specified'}
                </div>
                <div className="text-sm text-muted-foreground">
                  Based on work history and skills analysis
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Skills and Experience */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Skills */}
          {analysisData?.cv_content?.skills && analysisData.cv_content.skills.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-600" />
                  Skills ({analysisData.cv_content.skills.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {analysisData.cv_content.skills.slice(0, 20).map((skill: any, index: number) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {typeof skill === 'string' ? skill : skill.name || skill}
                    </Badge>
                  ))}
                  {analysisData.cv_content.skills.length > 20 && (
                    <Badge variant="outline" className="text-xs">
                      +{analysisData.cv_content.skills.length - 20} more
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Education */}
          {analysisData?.cv_content?.education && analysisData.cv_content.education.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <GraduationCap className="h-5 w-5 text-purple-600" />
                  Education ({analysisData.cv_content.education.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analysisData.cv_content.education.slice(0, 3).map((edu: any, index: number) => (
                    <div key={index} className="border-l-2 border-purple-200 pl-3">
                      <div className="font-medium">{edu.degree || 'Degree'}</div>
                      <div className="text-sm text-muted-foreground">
                        {edu.institution || 'Institution'}
                        {edu.field_of_study && ` • ${edu.field_of_study}`}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}