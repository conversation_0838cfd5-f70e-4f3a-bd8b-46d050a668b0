/**
 * Security utilities for file validation and protection
 */

// File type validation
export const ALLOWED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
} as const;

export const ALLOWED_EXTENSIONS = ['.pdf', '.doc', '.docx'] as const;

// File size limits
export const MAX_FILE_SIZE = 4 * 1024 * 1024; // 4MB in bytes
export const MIN_FILE_SIZE = 1024; // 1KB minimum

// Security patterns to detect malicious content
const MALICIOUS_PATTERNS = [
  // Script injection patterns
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /onload\s*=/gi,
  /onerror\s*=/gi,

  // File path traversal
  /\.\.\//g,
  /\.\.\\\\/g,

  // Executable extensions in filename
  /\.(exe|bat|cmd|com|pif|scr|vbs|js|jar|app|deb|pkg|dmg)$/i,

  // Suspicious file headers (magic numbers)
  /^MZ/, // PE executable
  /^PK/, // ZIP archive (could contain malicious files)
];

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fileInfo: {
    name: string;
    size: number;
    type: string;
    extension: string;
  };
}

/**
 * Validates file type based on MIME type and extension
 */
export function validateFileType(file: File): { isValid: boolean; error?: string } {
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

  // Check if extension is allowed
  if (!ALLOWED_EXTENSIONS.includes(fileExtension as any)) {
    return {
      isValid: false,
      error: `File type not allowed. Supported formats: ${ALLOWED_EXTENSIONS.join(', ')}`
    };
  }

  // Check MIME type
  if (!Object.keys(ALLOWED_FILE_TYPES).includes(file.type)) {
    return {
      isValid: false,
      error: `Invalid MIME type. Expected: ${Object.keys(ALLOWED_FILE_TYPES).join(', ')}`
    };
  }

  // Cross-check MIME type with extension
  const allowedExtensions = ALLOWED_FILE_TYPES[file.type as keyof typeof ALLOWED_FILE_TYPES];
  if (!allowedExtensions || !(allowedExtensions as readonly string[]).includes(fileExtension)) {
    return {
      isValid: false,
      error: 'File extension does not match MIME type'
    };
  }

  return { isValid: true };
}

/**
 * Validates file size
 */
export function validateFileSize(file: File): { isValid: boolean; error?: string } {
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size exceeds maximum limit of ${Math.round(MAX_FILE_SIZE / (1024 * 1024))}MB`
    };
  }

  if (file.size < MIN_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size is too small. Minimum size is ${MIN_FILE_SIZE} bytes`
    };
  }

  return { isValid: true };
}

/**
 * Validates filename for security issues
 */
export function validateFileName(fileName: string): { isValid: boolean; error?: string } {
  // Check for null bytes
  if (fileName.includes('\0')) {
    return {
      isValid: false,
      error: 'Filename contains null bytes'
    };
  }

  // Check for path traversal
  if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
    return {
      isValid: false,
      error: 'Filename contains invalid characters'
    };
  }

  // Check for control characters
  if (/[\x00-\x1f\x7f-\x9f]/.test(fileName)) {
    return {
      isValid: false,
      error: 'Filename contains control characters'
    };
  }

  // Check for reserved names (Windows)
  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
  const nameWithoutExt = fileName.split('.')[0].toUpperCase();
  if (reservedNames.includes(nameWithoutExt)) {
    return {
      isValid: false,
      error: 'Filename uses reserved system name'
    };
  }

  // Check length
  if (fileName.length > 255) {
    return {
      isValid: false,
      error: 'Filename is too long'
    };
  }

  return { isValid: true };
}

/**
 * Scans file content for malicious patterns
 */
export async function scanFileContent(file: File): Promise<{ isValid: boolean; warnings: string[] }> {
  const warnings: string[] = [];

  try {
    // Read first 1KB of file for header analysis
    const headerBuffer = await file.slice(0, 1024).arrayBuffer();
    const headerText = new TextDecoder('utf-8', { fatal: false }).decode(headerBuffer);

    // Check for malicious patterns
    for (const pattern of MALICIOUS_PATTERNS) {
      if (pattern.test(headerText)) {
        warnings.push('Potentially suspicious content detected in file header');
        break;
      }
    }

    // Check for embedded files (ZIP signature in PDF)
    if (file.type === 'application/pdf' && headerText.includes('PK')) {
      warnings.push('PDF contains embedded archive - please verify content');
    }

    // Check for macros in Office documents
    if (file.name.toLowerCase().endsWith('.docx') || file.name.toLowerCase().endsWith('.doc')) {
      // This is a simplified check - in production, you'd want more sophisticated macro detection
      if (headerText.includes('macro') || headerText.includes('VBA')) {
        warnings.push('Document may contain macros - please verify safety');
      }
    }

  } catch (error) {
    warnings.push('Unable to scan file content completely');
  }

  return {
    isValid: warnings.length === 0,
    warnings
  };
}

/**
 * Comprehensive file validation
 */
export async function validateFile(file: File): Promise<FileValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Basic file info
  const fileInfo = {
    name: file.name,
    size: file.size,
    type: file.type,
    extension: '.' + file.name.split('.').pop()?.toLowerCase() || ''
  };

  // Validate filename
  const fileNameValidation = validateFileName(file.name);
  if (!fileNameValidation.isValid) {
    errors.push(fileNameValidation.error!);
  }

  // Validate file type
  const fileTypeValidation = validateFileType(file);
  if (!fileTypeValidation.isValid) {
    errors.push(fileTypeValidation.error!);
  }

  // Validate file size
  const fileSizeValidation = validateFileSize(file);
  if (!fileSizeValidation.isValid) {
    errors.push(fileSizeValidation.error!);
  }

  // Scan file content (only if basic validations pass)
  if (errors.length === 0) {
    const contentScan = await scanFileContent(file);
    warnings.push(...contentScan.warnings);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fileInfo
  };
}

/**
 * Sanitizes filename for safe storage
 */
export function sanitizeFileName(fileName: string): string {
  // Remove path components
  const baseName = fileName.split(/[/\\]/).pop() || 'unnamed';

  // Replace unsafe characters
  const sanitized = baseName
    .replace(/[^\w\s.-]/g, '_') // Replace non-alphanumeric chars except spaces, dots, hyphens
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^[._-]+|[._-]+$/g, ''); // Remove leading/trailing special chars

  // Ensure we have a valid filename
  if (!sanitized || sanitized.length === 0) {
    return 'unnamed_file';
  }

  // Truncate if too long (keep extension)
  if (sanitized.length > 100) {
    const parts = sanitized.split('.');
    const ext = parts.pop();
    const name = parts.join('.').substring(0, 90);
    return ext ? `${name}.${ext}` : name;
  }

  return sanitized;
}

/**
 * Rate limiting for file uploads
 */
export class UploadRateLimit {
  private attempts: Map<string, number[]> = new Map();
  private readonly maxAttempts: number;
  private readonly windowMs: number;

  constructor(maxAttempts = 5, windowMs = 15 * 60 * 1000) { // 5 attempts per 15 minutes
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(identifier) || [];

    // Remove old attempts outside the window
    const recentAttempts = attempts.filter(time => now - time < this.windowMs);

    // Update the attempts list
    this.attempts.set(identifier, recentAttempts);

    return recentAttempts.length < this.maxAttempts;
  }

  recordAttempt(identifier: string): void {
    const now = Date.now();
    const attempts = this.attempts.get(identifier) || [];
    attempts.push(now);
    this.attempts.set(identifier, attempts);
  }

  getRemainingAttempts(identifier: string): number {
    const now = Date.now();
    const attempts = this.attempts.get(identifier) || [];
    const recentAttempts = attempts.filter(time => now - time < this.windowMs);
    return Math.max(0, this.maxAttempts - recentAttempts.length);
  }

  getResetTime(identifier: string): number {
    const attempts = this.attempts.get(identifier) || [];
    if (attempts.length === 0) return 0;

    const oldestAttempt = Math.min(...attempts);
    return oldestAttempt + this.windowMs;
  }
}
