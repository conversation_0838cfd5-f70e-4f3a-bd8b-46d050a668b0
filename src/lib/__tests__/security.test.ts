import {
  validateFileType,
  validateFileSize,
  validateFileName,
  validateFile,
  sanitizeFileName,
  UploadRateLimit,
  MAX_FILE_SIZE,
  MIN_FILE_SIZE,
  ALLOWED_EXTENSIONS
} from '../security'

// Mock File constructor for tests
const createMockFile = (name: string, size: number, type: string): File => {
  const file = new File([''], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

describe('Security Validation', () => {
  describe('validateFileType', () => {
    it('should accept valid PDF files', () => {
      const file = createMockFile('resume.pdf', 1000, 'application/pdf')
      const result = validateFileType(file)
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept valid DOC files', () => {
      const file = createMockFile('resume.doc', 1000, 'application/msword')
      const result = validateFileType(file)
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should accept valid DOCX files', () => {
      const file = createMockFile('resume.docx', 1000, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
      const result = validateFileType(file)
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should reject files with invalid extensions', () => {
      const file = createMockFile('resume.txt', 1000, 'text/plain')
      const result = validateFileType(file)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('File type not allowed')
    })

    it('should reject files with invalid MIME types', () => {
      const file = createMockFile('resume.pdf', 1000, 'text/plain')
      const result = validateFileType(file)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Invalid MIME type')
    })

    it('should reject files with mismatched extension and MIME type', () => {
      const file = createMockFile('resume.pdf', 1000, 'application/msword')
      const result = validateFileType(file)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('File extension does not match MIME type')
    })
  })

  describe('validateFileSize', () => {
    it('should accept files within size limits', () => {
      const file = createMockFile('resume.pdf', 2 * 1024 * 1024, 'application/pdf') // 2MB
      const result = validateFileSize(file)
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should reject files that are too large', () => {
      const file = createMockFile('resume.pdf', MAX_FILE_SIZE + 1, 'application/pdf')
      const result = validateFileSize(file)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('File size exceeds maximum limit')
    })

    it('should reject files that are too small', () => {
      const file = createMockFile('resume.pdf', MIN_FILE_SIZE - 1, 'application/pdf')
      const result = validateFileSize(file)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('File size is too small')
    })
  })

  describe('validateFileName', () => {
    it('should accept valid filenames', () => {
      const result = validateFileName('resume.pdf')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should reject filenames with null bytes', () => {
      const result = validateFileName('resume\0.pdf')
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('null bytes')
    })

    it('should reject filenames with path traversal', () => {
      const result = validateFileName('../resume.pdf')
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('invalid characters')
    })

    it('should reject filenames with control characters', () => {
      const result = validateFileName('resume\x01.pdf')
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('control characters')
    })

    it('should reject reserved system names', () => {
      const result = validateFileName('CON.pdf')
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('reserved system name')
    })

    it('should reject filenames that are too long', () => {
      const longName = 'a'.repeat(256) + '.pdf'
      const result = validateFileName(longName)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('too long')
    })
  })

  describe('validateFile', () => {
    it('should validate a completely valid file', async () => {
      const file = createMockFile('resume.pdf', 2 * 1024 * 1024, 'application/pdf')
      const result = await validateFile(file)

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.fileInfo.name).toBe('resume.pdf')
      expect(result.fileInfo.size).toBe(2 * 1024 * 1024)
      expect(result.fileInfo.type).toBe('application/pdf')
      expect(result.fileInfo.extension).toBe('.pdf')
    })

    it('should collect all validation errors', async () => {
      const file = createMockFile('../invalid\0.txt', MAX_FILE_SIZE + 1, 'text/plain')
      const result = await validateFile(file)

      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('sanitizeFileName', () => {
    it('should sanitize unsafe characters', () => {
      const result = sanitizeFileName('my<>resume|file.pdf')
      expect(result).toBe('my_resume_file.pdf')
    })

    it('should replace spaces with underscores', () => {
      const result = sanitizeFileName('my resume file.pdf')
      expect(result).toBe('my_resume_file.pdf')
    })

    it('should remove path components', () => {
      const result = sanitizeFileName('../path/to/resume.pdf')
      expect(result).toBe('resume.pdf')
    })

    it('should handle empty or invalid filenames', () => {
      const result = sanitizeFileName('')
      expect(result).toBe('unnamed')
    })

    it('should truncate very long filenames', () => {
      const longName = 'a'.repeat(200) + '.pdf'
      const result = sanitizeFileName(longName)
      expect(result.length).toBeLessThanOrEqual(100)
      expect(result).toMatch(/\.pdf$/)
    })
  })

  describe('UploadRateLimit', () => {
    let rateLimit: UploadRateLimit

    beforeEach(() => {
      rateLimit = new UploadRateLimit(3, 1000) // 3 attempts per second for testing
    })

    it('should allow uploads within the limit', () => {
      expect(rateLimit.isAllowed('user1')).toBe(true)
      rateLimit.recordAttempt('user1')
      expect(rateLimit.isAllowed('user1')).toBe(true)
      rateLimit.recordAttempt('user1')
      expect(rateLimit.isAllowed('user1')).toBe(true)
    })

    it('should block uploads after exceeding the limit', () => {
      // Use up all attempts
      for (let i = 0; i < 3; i++) {
        rateLimit.recordAttempt('user1')
      }
      expect(rateLimit.isAllowed('user1')).toBe(false)
    })

    it('should track different users separately', () => {
      // Use up attempts for user1
      for (let i = 0; i < 3; i++) {
        rateLimit.recordAttempt('user1')
      }

      // user2 should still be allowed
      expect(rateLimit.isAllowed('user2')).toBe(true)
    })

    it('should return correct remaining attempts', () => {
      expect(rateLimit.getRemainingAttempts('user1')).toBe(3)
      rateLimit.recordAttempt('user1')
      expect(rateLimit.getRemainingAttempts('user1')).toBe(2)
    })

    it('should reset after time window', (done) => {
      // Use up all attempts
      for (let i = 0; i < 3; i++) {
        rateLimit.recordAttempt('user1')
      }
      expect(rateLimit.isAllowed('user1')).toBe(false)

      // Wait for reset (using a short window for testing)
      setTimeout(() => {
        expect(rateLimit.isAllowed('user1')).toBe(true)
        done()
      }, 1100) // Wait slightly longer than the window
    })
  })
})
